# fly.toml app configuration file generated for notion-cre on 2024-01-14T21:58:00+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "notion-cre"
primary_region = "sjc"

[build]

[http_service]
  internal_port = 4321
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 256
