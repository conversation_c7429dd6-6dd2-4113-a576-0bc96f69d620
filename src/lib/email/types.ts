export interface IEmail<T = any> {
  to: string
  name?: string
  subject: string
  props?: T
}

export interface ZeptoOTPEmailProps {
  name: string
  OTP: string
}

export interface ZeptoWelcomeEmailProps {
  user: string
  url: string
  type: 'lp' | 'founder'
}

export interface IEmailClient {
  sendWelcomeEmail: <T>(email: IEmail<T>) => Promise<unknown>
  sendOTP: <T>(email: IEmail<T>) => Promise<unknown>
}
