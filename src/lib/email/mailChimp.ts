import MailChimp from '@mailchimp/mailchimp_transactional'
import { CRE_EMAIL_SENDER, MAIL_CHIMP_KEY } from '../../server-constants'

import type {
  IEmailClient,
  IEmail,
  ZeptoOTPEmailProps,
  ZeptoWelcomeEmailProps,
} from './types'
import { welcomeEmail, lpWelcomeEmail } from './welcome'
import { otpEmail } from './otp'

const mailchimpTx = MailChimp(MAIL_CHIMP_KEY)

const sendWelcomeEmail = async (email: IEmail<ZeptoWelcomeEmailProps>) => {
  const type = email?.props?.type
  let message = {
    from_email: CRE_EMAIL_SENDER,
    subject: 'Welcome to CRE',
    html: welcomeEmail(email.name),
    to: [
      {
        email: email.to,
        type: 'to',
      },
    ],
  }

  if (type === 'lp') {
    message = {
      from_email: CRE_EMAIL_SENDER,
      subject: 'Welcome to CRE',
      html: lpWelcomeEmail(email?.props?.user, email?.props?.url),
      to: [
        {
          email: email.to,
          type: 'to',
        },
      ],
    }
  }

  return await mailchimpTx.messages.send({ message: message })
}

const sendOTP = async (email: IEmail<ZeptoOTPEmailProps>) => {
  const message = {
    from_email: CRE_EMAIL_SENDER,
    subject: 'OTP request',
    html: otpEmail(email?.props?.name, email?.props?.OTP),
    to: [
      {
        email: email.to,
        type: 'to',
      },
    ],
  }

  return await mailchimpTx.messages.send({ message: message })
}

export const mailchimp: IEmailClient = {
  // @ts-ignore
  sendWelcomeEmail,
  // @ts-ignore
  sendOTP,
}
