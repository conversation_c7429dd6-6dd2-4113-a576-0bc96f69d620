export const otpEmail = (name?: string, otp?: string) => {
  return `
  <table cellspacing="0" cellpadding="0" style="background-color: #F4F6F7; border: 1px solid #eee; width: 100%;">
    <tbody>
        <tr>
            <td>
                <div
                    style="background-color: #fff; border: 1px solid #DEE6E9; border-radius: 10px; box-sizing: border-box; font-family: Lato, Helvetica, 'Helvetica Neue', Arial, 'sans-serif'; margin: auto; max-width: 600px; overflow: hidden; width: 600px;">
                    <div style="background-color: #fff; padding: 40px; text-align: center;">
                        <img alt="CRE.vc" style="border-radius: 50%;" src="https://resources.cre.vc/logo-high.png" width="107" height="107"><br>
                    </div>
                    <div
                        style="padding: 40px 50px; background-image: url(../images/sampleTemplates/shadow.svg); background-repeat: no-repeat; background-position: top; background-size: contain;">
                        <p style="margin: 0px 0px 25px;">
                            <span class="size" style="font-size: 14px; margin: 0px 0px 25px;">Hi ${name}</span><br></p>
                        <p style="margin: 0px 0px 35px; line-height: 22px;">
                            <span class="size" style="font-size: 16px; margin: 0px 0px 35px; line-height: 22px;">Below is your <b>One time password:</b></span><br>
                        </p>
                        <div style="text-align: center;">
                            <div
                                style="background-color: #25586B0D; border-radius: 6px; color: #25586B; display: inline-block; font-size: 30px; padding: 20px 30px;">
                                ${otp}</div><br>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: center; margin-top: 15px;">
                            <div
                                style="background-image: url(../images/sampleTemplates/copy.svg); background-repeat: no-repeat; background-size: contain; height: 14px; width: 14px;">
                                <br></div>
                        </div>
                        <p style="margin: 35px 0px; line-height: 22px;">
                            <span class="size" style="font-size: 14px; margin: 35px 0px; line-height: 22px;">This access request will expire in 15 minutes.</span><br>
                        </p>
                        <p style="margin: 0px; line-height: 22px;">
                            <span class="size" style="font-size: 14px; margin: 0px; line-height: 22px;">Thank you,</span><br>
                        </p>
                        <p style="margin: 0px; line-height: 22px;">CRE</p>
                    </div>
                </div>
            </td>
        </tr>
    </tbody>
</table>
<div><br></div>
  `
}
