import { SendMailClient } from 'zeptomail'
import { ZEPTO_API_KEY, CRE_EMAIL_SENDER } from '../../server-constants'
import type {
  IEmailClient,
  IEmail,
  ZeptoOTPEmailProps,
  ZeptoWelcomeEmailProps,
} from './types'

const url = 'api.zeptomail.com/v1.1/email/template'
const token = ZEPTO_API_KEY

const client = new SendMailClient({ url, token })

const sendWelcomeEmail = async <T = ZeptoWelcomeEmailProps>(
  email: IEmail<T>
) => {
  return await client.sendMail({
    mail_template_key:
      '2d6f.1d90834273e54aed.k1.86c634a0-7f28-11ee-bfb1-525400ae9113.18bb53554ea',
    from: {
      address: CRE_EMAIL_SENDER,
      name: 'noreply',
    },
    to: [
      {
        email_address: {
          address: email.to,
          name: email.name,
        },
      },
    ],
    merge_info: { ...email.props },
    subject: email.subject,
  })
}

const sendOTP = async <T = ZeptoOTPEmailProps>(email: IEmail<T>) => {
  return await client.sendMail({
    mail_template_key:
      '2d6f.1d90834273e54aed.k1.2225aa90-7f27-11ee-bfb1-525400ae9113.18bb52c33b9',
    from: {
      address: CRE_EMAIL_SENDER,
      name: 'noreply',
    },
    to: [
      {
        email_address: {
          address: email.to,
          name: email.name,
        },
      },
    ],
    merge_info: { ...email.props },
    subject: email.subject,
  })
}

export const zepto: IEmailClient = {
  sendWelcomeEmail,
  sendOTP,
}
