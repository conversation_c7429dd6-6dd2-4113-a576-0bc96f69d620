import mongoose from 'mongoose'
import { type ConnectOptions } from 'mongoose'
const MONGODB_URI = process.env.MONGODB_URI as string

const MONGODB_OPTIONS: ConnectOptions = {}

mongoose.connect(MONGODB_URI, MONGODB_OPTIONS)

const db = mongoose.connection

db.on('error', console.error.bind(console, 'connection error:'))
db.once('open', () => {
  console.log('Connected to MongoDB database!')
})

export default db
