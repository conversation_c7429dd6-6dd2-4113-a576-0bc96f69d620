import mongoose, { Schema, Document } from 'mongoose'
import {
  type IUser,
  accessLevels,
  vcType,
  seedType,
  type SecretsUserLocalUserAssociation,
} from '../types'
import { logsSchema } from './logs'

const userSchema = new Schema<IUser & Document>({
  fullname: { type: String, required: false },
  email: { type: String, required: true, unique: true },
  accessLevel: { type: String, enum: accessLevels, required: true },
  vcType: { type: String, enum: vcType, required: true },
  seedType: { type: String, enum: seedType, required: false },
  company: { type: String, required: false },
  role: { type: String, required: false },
  lastLoggedIn: { type: Date, default: null },
  createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  logs: [logsSchema],
  active: { type: Boolean, default: true },
})

const User = mongoose.model<IUser & Document>('User', userSchema)

const secretsUserAssociation = new Schema<SecretsUserLocalUserAssociation>({
  createdAt: { type: Date, default: Date.now },
  userID: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  secretID: { type: Schema.Types.Number },
})

const SecretsUserAssociation = mongoose.model<SecretsUserLocalUserAssociation>(
  'SecretsUserAssociation',
  secretsUserAssociation
)

export { User, SecretsUserAssociation }
