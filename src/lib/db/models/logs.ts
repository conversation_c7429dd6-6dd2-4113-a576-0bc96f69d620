import mongoose, { Schem<PERSON> } from 'mongoose'
import type { ILog } from '../types'

const logsSchema = new Schema<ILog>({
  timestamp: { type: Date, default: Date.now },
  action: { type: String, required: true },
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  changes: { type: Schema.Types.Mixed, required: true },
})

const Logs = mongoose.model<ILog>('Logs', logsSchema)

export { Logs, logsSchema }
