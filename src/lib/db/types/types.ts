import { Document, Schema } from 'mongoose'
export const accessLevels = ['admin', 'moderator', 'user'] as const
export const vcType = ['lp', 'founder'] as const
export const seedType = [
  'pre-seed',
  'seed',
  'series a',
  'series b',
  'series c',
  'series d',
]
export type AccessLevel = (typeof accessLevels)[number]
export type VcType = (typeof vcType)[number]
export type SeedType = (typeof seedType)[number]

export interface IUser {
  fullname?: string
  email: string
  accessLevel: AccessLevel
  vcType: VcType
  seedType?: SeedType
  company?: string
  role?: string
  lastLoggedIn?: Date | null
  createdBy: string | (IUser & Document)
  createdAt?: Date
  logs?: any[]
  active?: boolean
}

export interface ILog extends Document {
  timestamp: Date
  action: string
  user: Schema.Types.ObjectId
  changes: any
}

export interface IOtp extends Document {
  code: string
  email: string
}

export interface SecretsUser {
  id: number
  email: string
  sign_in_url?: string
}

export interface SecretsUserLocalUserAssociation extends Document {
  userID: Schema.Types.ObjectId
  secretID: number
  createdAt?: Date
}
