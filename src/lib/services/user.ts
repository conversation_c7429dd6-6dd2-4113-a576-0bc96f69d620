import { BASE_PATH, CUSTOM_DOMAIN } from '../../server-constants'
import { User } from '../db'
import emailClient from '../email'
import type { IUserCreate, IUserUpdate, QueryParams } from '../schemas'
import { OTPService } from './otp'

const getSite = function () {
  if (CUSTOM_DOMAIN)
    return new URL(BASE_PATH, `https://${CUSTOM_DOMAIN}`).toString()
  if (process.env.VERCEL && process.env.VERCEL_URL)
    return new URL(BASE_PATH, `https://${process.env.VERCEL_URL}`).toString()

  return new URL(BASE_PATH, 'http://localhost:3000').toString()
}

export class UserService {
  async createUser(user: IUserCreate) {
    const _user = new User(user)
    return await _user.save()
  }
  async getUserById(id: string) {
    return await User.findById(id)
  }
  async getUserByEmail(email: string) {
    return await User.find({ email })
  }
  async updateUser(id: string, user: IUserUpdate) {
    return await User.findByIdAndUpdate(id, user, { new: true })
  }
  async deleteUser(id: string) {
    return await User.findByIdAndDelete(id)
  }
  async requestOTP(email: string) {
    const [user] = await this.getUserByEmail(email)
    if (user) {
      const otpService = new OTPService()
      const [otp, signature, _] = await otpService.getAndStoreSignatureForEmail(
        user._id
      )
      if (otp) {
        // console.log('OTP', otp)
        await emailClient
          .sendOTP({
            to: user.email,
            subject: 'Your OTP',
            props: {
              name: user?.fullname ?? 'User',
              OTP: otp,
            },
          })
          .catch((err) => {
            console.log('Error sending OTP', err)
            console.log('OTP', err?.error?.details)
          })
      }
      return true
    }
    return false
  }

  async sendWelcomeEmail(email: string, type: 'lp' | 'founder') {
    const [user] = await this.getUserByEmail(email)
    if (user) {
      await emailClient.sendWelcomeEmail({
        to: user.email,
        subject: 'Welcome to the platform',
        props: {
          user: user?.fullname ?? 'User',
          url: 'https://resources.cre.vc/',
          type,
        },
      })
      return true
    }
    return false
  }

  async verifyOTP(email: string, otp: string) {
    const [user] = await this.getUserByEmail(email)
    if (!user) {
      return false
    }

    const otpService = new OTPService()
    const isVerified = await otpService.verifyAndDeleteSignatureForEmail(
      user._id,
      otp
    )
    return { user, isVerified }
  }

  async updateLastLoggedIn(id: string) {
    return await User.findByIdAndUpdate(
      id,
      { lastLoggedIn: Date.now() },
      { new: true }
    )
  }

  async getUsers(
    query: QueryParams = { skip: 0, limit: 10000, filter: {}, sort: {} }
  ) {
    const { limit, skip, sort, filter } = query
    const users = await User.find({ ...filter })
      .limit(limit ?? 10000)
      .skip(skip ?? 0)
      .sort(sort ?? { lastLoggedIn: -1 })
    const total = await User.countDocuments()
    return { users, total }
  }
}
