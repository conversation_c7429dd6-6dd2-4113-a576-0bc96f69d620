import * as crypto from 'crypto'
import { SECRET_KEY } from '../../server-constants'
import { OTP as OTPModel } from '../db'

class OTP {
  secret: string
  length: number
  otp_chars: string[]
  expires_after: number
  user_identifier: string

  constructor(
    secret: string,
    length = 7,
    otp_chars: string[] | null = null,
    expires_after = 15,
    user_identifier: string | null = null
  ) {
    this.secret = secret
    this.length = length
    this.expires_after = expires_after
    this.user_identifier = user_identifier || 'Secret'
    this.otp_chars = otp_chars || Array.from('0123456789')
  }

  private generateOTP(): string {
    return Array.from(
      { length: this.length },
      () => this.otp_chars[Math.floor(Math.random() * this.otp_chars.length)]
    ).join('')
  }

  private generateHashString(otp: string, expiry: number): string {
    return `${this.user_identifier}.${otp}.${expiry}`
  }

  private hmac(hash_string: string): string {
    return crypto
      .createHmac('sha256', this.secret)
      .update(hash_string)
      .digest('hex')
  }

  generate(): [string, string] {
    const otp = this.generateOTP()
    const expiry = Math.floor(Date.now() / 1000) + this.expires_after * 60
    const hash_string = this.generateHashString(otp, expiry)
    const dig = this.hmac(hash_string)
    const signature = `${dig}.${expiry}`

    return [otp, signature]
  }

  verify(otp: string, signature: string): boolean {
    const [dig, expiry] = signature.split('.')

    if (expiry && +expiry * 1000 > Date.now()) {
      const hash_string = this.generateHashString(otp, expiry)
      const n_dig = this.hmac(hash_string)

      return crypto.timingSafeEqual(
        Buffer.from(dig, 'hex'),
        Buffer.from(n_dig, 'hex')
      )
    }

    return false
  }
}

class OTPService {
  async generateOTPForEmail(userid: string): Promise<[string, string]> {
    const otp = new OTP(SECRET_KEY, 6, null, 15, userid)
    return otp.generate()
  }

  async verifyOTPForEmail(
    userid: string,
    otp: string,
    signature: string
  ): Promise<boolean> {
    const otpObj = new OTP(SECRET_KEY, 6, null, 15, userid)
    return otpObj.verify(otp, signature)
  }

  async storeSignatureForEmail(
    userid: string,
    signature: string
  ): Promise<unknown> {
    const _ = new OTPModel({ code: signature, userid: userid })
    return await _.save()
  }

  async getSignatureForEmail(userid: string): Promise<unknown> {
    return await OTPModel.findOne({ userid: userid })
  }

  async getAndStoreSignatureForEmail(
    userid: string
  ): Promise<[string, string, unknown]> {
    await OTPModel.findOneAndDelete({ userid: userid }) // ensure that there is only one signature for a user
    const [otp, signature] = await this.generateOTPForEmail(userid)
    const _ = await this.storeSignatureForEmail(userid, signature)
    return [otp, signature, _]
  }

  async verifyAndDeleteSignatureForEmail(
    userid: string,
    otp: string
  ): Promise<boolean> {
    const signature = await this.getSignatureForEmail(userid)

    if (!signature || !signature.code) {
      return false
    }
    const isVerified = await this.verifyOTPForEmail(userid, otp, signature.code)
    if (isVerified) {
      await OTPModel.findOneAndDelete({ userid: userid })
    }
    return isVerified
  }

  async deleteSignatureForEmail(userid: string): Promise<unknown> {
    return await OTPModel.findOneAndDelete({ userid: userid })
  }
}

export { OTP, OTPService }
