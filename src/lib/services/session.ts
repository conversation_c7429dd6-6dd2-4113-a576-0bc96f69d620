import { Session, sessionSchema } from '../db'
import { UserService } from './user'

export class SessionService {
  async createSession(userId: string) {
    const session = new Session({
      userId,
    })
    return await session.save()
  }

  async getSessionById(id: string) {
    return await Session.findById(id)
  }

  async deleteSession(id: string) {
    return await Session.findByIdAndDelete(id)
  }

  async deleteSessionsByUserId(userId: string) {
    return await Session.deleteMany({ userId })
  }

  async getSessionsByUserId(userId: string) {
    return await Session.find({ userId })
  }

  async deleteExpiredSessions() {
    return await Session.deleteMany({ createdAt: { $lt: new Date() } })
  }

  async checkSessionIsActiveAndGetCurrentUser(id: string | undefined) {
    if (!id) {
      return false
    }
    const session = await this.getSessionById(id)
    if (!session) {
      return false
    }
    const user_service = new UserService()
    const user = await user_service.getUserById(session.userId.toString())
    if (!user) {
      return null
    }
    return user
  }
}
