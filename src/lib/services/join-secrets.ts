import mongoose, { <PERSON>hem<PERSON>, Document } from 'mongoose'
import { SecretsUserAssociation, type IUser } from '../db'
import { JOIN_SECRETS_PRIVATE_KEY } from '../../server-constants'

interface SecretCategory {
  id: number
  name_i18n: {
    en: string
    fr: string
  }
  slug: string
}

interface SecretsUser {
  id: number
  email: string
  sign_in_url: string
}

interface SecretsUsersResponse {
  users_count: number
  users: SecretsUser[]
  sign_in_url_example: string
}

export class JoinSecretsService {
  private readonly BASE_URL = 'https://www.joinsecret.com/api/v1'

  private async makeAuthenticatedRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T | null> {
    try {
      const token = await this.getSecretsToken()
      if (!token) {
        throw new Error('Could not get token for authentication')
      }

      const response = await fetch(`${this.BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          Authorization: `Bearer ${token.jwt_token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      if (response.ok) {
        return await response.json()
      }

      // Store the status code for handling specific cases
      const statusCode = response.status
      if (statusCode === 422) {
        return null
      }

      throw new Error(`Request failed with status: ${statusCode}`)
    } catch (error) {
      console.error(`Error making request to ${endpoint}:`, error)
      return null
    }
  }

  private async findExistingSecretsUser(
    email: string
  ): Promise<SecretsUser | null> {
    const allUsers = await this.getAllSecretsUsers()
    if (!allUsers?.users) return null

    return (
      allUsers.users.find(
        (user) => user.email.toLowerCase() === email.toLowerCase()
      ) || null
    )
  }

  async getAllSecretsUsers(): Promise<SecretsUsersResponse | null> {
    return await this.makeAuthenticatedRequest<SecretsUsersResponse>('/users')
  }

  async createSecretsUser(user: IUser & Document) {
    // First check our local association
    const existingAssociation = await this.getSecretsUserWithLocalUserId(
      user.id
    )
    if (existingAssociation) {
      return existingAssociation
    }

    // Check if user exists in Secrets but not in our association
    const existingSecretsUser = await this.findExistingSecretsUser(user.email)
    if (existingSecretsUser) {
      // Create association for existing Secrets user
      const association = new SecretsUserAssociation({
        userID: user.id,
        secretID: existingSecretsUser.id.toString(),
      })
      return await association.save()
    }

    // If user doesn't exist in Secrets, create new user
    const secretUserCreatedResponse = await this.makeAuthenticatedRequest<{
      id: string
      sign_in_url: string
    }>('/users', {
      method: 'POST',
      body: JSON.stringify({
        email: user.email,
      }),
    })

    if (!secretUserCreatedResponse) return null

    const association = new SecretsUserAssociation({
      userID: user.id,
      secretID: secretUserCreatedResponse.id,
    })

    return await association.save()
  }

  async getSecretsUserWithLocalUserId(id: string) {
    return await SecretsUserAssociation.findOne({
      userID: id,
    })
  }

  async getSecretsToken() {
    try {
      const response = await fetch(`${this.BASE_URL}/authentications`, {
        headers: {
          Authorization: `Bearer ${JOIN_SECRETS_PRIVATE_KEY}`,
        },
        method: 'POST',
      })

      if (response.status === 201) {
        return (await response.json()) as {
          success: string
          jwt_token: string
          exp: Date
        }
      }
      return null
    } catch (error) {
      console.error('Error getting secrets token:', error)
      return null
    }
  }

  async getSignInURL(userID: string) {
    const secretsUser = await this.getSecretsUserWithLocalUserId(userID)
    if (!secretsUser) {
      return null
    }

    return await this.makeAuthenticatedRequest<{
      user_id: number
      sign_in_url: string
      single_use_sign_in_url: string
    }>(`/users/${secretsUser.secretID}/single_use_signin_urls`, {
      method: 'POST',
    })
  }

  async getSignInURLDirectly(id: number) {
    return await this.makeAuthenticatedRequest<{
      user_id: number
      sign_in_url: string
      single_use_sign_in_url: string
    }>(`/users/${id}/single_use_signin_urls`, {
      method: 'POST',
    })
  }

  async getCategories(): Promise<SecretCategory[] | null> {
    return await this.makeAuthenticatedRequest<SecretCategory[]>('/categories')
  }
}
