import type { AstroIntegration } from 'astro'
export default (): AstroIntegration => ({
  name: 'validate-env',
  hooks: {
    'astro:config:setup': async () => {
      const env = process.env
      const requiredEnv = [
        'SALEM_NOTION_API_SECRET',
        'FOUNDERS_DATABASE_ID',
        'LP_DATABASE_ID',
        'MONGODB_URI',
        'ZEPTO_API_KEY',
        'CRE_EMAIL_SENDER',
        'SECRET_KEY',
        'PUBLIC_SECRET_KEY',
        'MAIL_CHIMP_KEY',
        'PUBLIC_HCAPTCHA_SITE_KEY',
        'HCAPTCHA_SECRET_KEY',
        // 'JOIN_SECRETS_PRIVATE_KEY',
        // 'CUSTOM_DOMAIN',
      ]
      const missingEnv = requiredEnv.filter((key) => !env[key])
      if (missingEnv.length > 0) {
        throw new Error(
          `Missing required environment variables: ${missingEnv.join(', ')}`
        )
      } else {
        console.log('All required environment variables are set.')
      }
    },
  },
})
