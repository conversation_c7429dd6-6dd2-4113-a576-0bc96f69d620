export const SALEM_NOTION_API_SECRET =
  import.meta.env.SALEM_NOTION_API_SECRET ||
  process.env.SALEM_NOTION_API_SECRET ||
  ''

export const JOIN_SECRETS_PRIVATE_KEY =
  import.meta.env.JOIN_SECRETS_PRIVATE_KEY ||
  process.env.JOIN_SECRETS_PRIVATE_KEY ||
  ''

export const FOUNDERS_DATABASE_ID =
  import.meta.env.FOUNDERS_DATABASE_ID || process.env.FOUNDERS_DATABASE_ID || ''
export const LP_DATABASE_ID =
  import.meta.env.LP_DATABASE_ID || process.env.LP_DATABASE_ID || ''
export const CUSTOM_DOMAIN =
  import.meta.env.CUSTOM_DOMAIN || process.env.CUSTOM_DOMAIN || '' // <- Set your costom domain if you have. e.g. alpacat.com
export const BASE_PATH =
  import.meta.env.BASE_PATH || process.env.BASE_PATH || '' // <- Set sub directory path if you want. e.g. /docs/

export const PUBLIC_GA_TRACKING_ID = import.meta.env.PUBLIC_GA_TRACKING_ID
export const NUMBER_OF_POSTS_PER_PAGE = 10
export const REQUEST_TIMEOUT_MS = parseInt(
  import.meta.env.REQUEST_TIMEOUT_MS || '10000',
  10
)
export const ENABLE_LIGHTBOX = import.meta.env.ENABLE_LIGHTBOX
export const MONGODB_URI =
  import.meta.env.MONGODB_URI || process.env.MONGODB_URI || ''
export const ZEPTO_API_KEY =
  import.meta.env.ZEPTO_API_KEY || process.env.ZEPTO_API_KEY || ''
export const CRE_EMAIL_SENDER =
  import.meta.env.CRE_EMAIL_SENDER || process.env.CRE_EMAIL_SENDER || ''
export const SECRET_KEY =
  import.meta.env.SECRET_KEY || process.env.SECRET_KEY || ''

export const MAIL_CHIMP_KEY =
  import.meta.env.MAIL_CHIMP_KEY || process.env.MAIL_CHIMP_KEY || ''

export const GITHUB_TOKEN =
  import.meta.env.GITHUB_TOKEN || process.env.GITHUB_TOKEN || ''
export const GITHUB_REPO =
  import.meta.env.GITHUB_REPO || process.env.GITHUB_REPO || ''
export const GITHUB_OWNER =
  import.meta.env.GITHUB_OWNER || process.env.GITHUB_OWNER || ''
