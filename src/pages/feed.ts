import rss from '@astrojs/rss'
import { getAllPosts } from '../lib/notion/client'
import { getPostLink } from '../lib/blog-helpers'

export async function GET() {
  const [posts] = await Promise.all([getAllPosts()])

  return rss({
    title: 'CRE Resource Library',
    description: 'A collection of resources for CRE',
    site: import.meta.env.SITE,
    items: posts.map((post) => ({
      link: new URL(getPostLink(post.PageId), import.meta.env.SITE).toString(),
      title: post.Title,
      // pubDate: new Date(post.Date),
      pubDate: new Date(), // some dates from notion are invalid
    })),
  })
}
