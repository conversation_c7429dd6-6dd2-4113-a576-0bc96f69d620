---
import AuthenticationChecker from '../../components/svelte/AuthenticationChecker.svelte'
import NotAllowed from '../../components/NotAllowed.astro'
import Layout from '../../layouts/Layout.astro'
import TableOfContents from '../../components/svelte/TableOfContents.svelte'
import Copyright from '../../components/Copyright.astro'
import Menu from '../../components/svelte/Menu.svelte'
import SearchBox from '../../components/svelte/SearchBox.svelte'
import {
  downloadFile,
  getAllCategories,
  getAllCategoriesForFounder,
  getAllCategoriesForLP,
  getAllPosts,
} from '../../lib/notion/client'
import Home from '../../components/svelte/Home.svelte'
import NewsDisplay from '../../components/svelte/NewsDisplay.svelte'
import { type SelectProperty } from '../../lib/interfaces'
import SlackButton from '../../components/svelte/SB.svelte'

const [founderCategories, lpCategories, allPosts] = await Promise.all([
  getAllCategoriesForFounder(),
  getAllCategoriesForLP(),
  getAllPosts(),
])

export async function getStaticPaths() {
  const allCategories = await getAllCategories()
  return allCategories.map((category: SelectProperty) => ({
    params: { slug: category.id },
  }))
}

const { slug } = Astro.params

const generatePrev = (slug) => {
  let index = founderCategories.findIndex(
    (category: SelectProperty) =>
      category.id === slug && category.type === 'founder'
  )

  if (index !== -1) {
    if (index === 0) {
      return null
    }
    return founderCategories[index - 1]
  }
  index = lpCategories.findIndex(
    (category: SelectProperty) => category.id === slug && category.type === 'lp'
  )
  if (index !== -1) {
    if (index === 0) {
      return null
    }
    return lpCategories[index - 1]
  }
  return null
}

const generateNext = (slug) => {
  let index = founderCategories.findIndex(
    (category: SelectProperty) =>
      category.id === slug && category.type === 'founder'
  )

  if (index !== -1) {
    if (index === founderCategories.length - 1) {
      return null
    }
    return founderCategories[index + 1]
  }

  index = lpCategories.findIndex(
    (category: SelectProperty) => category.id === slug && category.type === 'lp'
  )
  if (index !== -1) {
    if (index + 1 === lpCategories.length) {
      return null
    }

    return lpCategories[index + 1]
  }
  return null
}

// Download all images and files for the exact section
const featuredImagesFor = (slug) => {
  const allImages = allPosts.filter((post) => {
    if (post.Category && post.Category?.id === slug) {
      return post
    }
  })

  return allImages
}

const allImages = featuredImagesFor(slug)
  .map((image) => image.FeaturedImage?.Url)
  .filter((url) => {
    if (url) {
      return url
    }
  })

const generateImageDownloadPromises = allImages.map((image) => {
  const url = new URL(image)
  return downloadFile(url)
})

await Promise.all(generateImageDownloadPromises).catch((error) => {
  console.error('Error downloading images', error)
})
---

<AuthenticationChecker client:load>
  <Layout
    slot="not-allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
  >
    <NotAllowed />
    <Copyright slot="footer" />
  </Layout>

  <Layout
    slot="allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
    search={true}
  >
    <div class="header" slot="header">
      <div class="search">
        <SearchBox client:load />
      </div>

      <div class="back-home">
        <a href="https://cre.vc"> Back to cre.vc</a>
        <SlackButton client:load />
      </div>

      <div class="menu">
        <Menu client:only />
      </div>
    </div>
    <TableOfContents
      client:only
      slot="sidebarLeft"
      lpCategories={lpCategories}
      founderCategories={founderCategories}
      allPosts={allPosts}
    />

    <Home
      client:only
      lpCategories={lpCategories}
      founderCategories={founderCategories}
      allPosts={allPosts}
      slug={slug}
      noShow
    />

    <NewsDisplay client:idle slot="sidebarRight" />
    <!-- <div class="pagination" slot="pageFooter">
      <div class="left">
        {
          generatePrev(slug) && (
            <a href={`/section/${generatePrev(slug)?.id ?? ''}?section_name=${encodeURIComponent(${generatePrev(slug)?.name ?? ''})}`}>
              <span class="arrow">&larr;</span> {generatePrev(slug)?.name ?? ''}
            </a>
          )
        }
      </div>
      <div class="right">
        {
          generateNext(slug) && (
            <a href={`/section/${generateNext(slug)?.id ?? ''}?section_name=${encodeURIComponent(${generateNext(slug)?.name ?? ''})}`}>
              {generateNext(slug)?.name ?? ''} <span class="arrow">&rarr;</span>
            </a>
          )
        }
      </div>
    </div> -->
    <Copyright slot="footer" />
  </Layout>
</AuthenticationChecker>

<style>
  .header {
    margin: auto;
    margin-right: 2rem;
  }
</style>
