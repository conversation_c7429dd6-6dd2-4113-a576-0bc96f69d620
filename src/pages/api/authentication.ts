import type { APIRoute } from 'astro'
import db from '../../lib/db/db'
import { UserService, SessionService } from '../../lib/services'
export const prerender = false

export const POST: APIRoute = async ({ request }) => {
  const { email, otp, passToken } = await request.json()

  if (!email && !otp) {
    return new Response(
      JSON.stringify({
        message: 'Missing required fields',
      }),
      { status: 400 }
    )
  }

  if (email && !otp) {
    if (!passToken) {
      return new Response(
        JSON.stringify({
          message: 'Missing Token',
        }),
        { status: 400 }
      )
    }

    const isVerified = await verifyHCaptcha(passToken)

    if (!isVerified) {
      return new Response(
        JSON.stringify({
          message: 'Invalid Token',
        }),
        { status: 400 }
      )
    }

    const userService = new UserService()
    const user = await userService.requestOTP(email)

    return new Response(
      JSON.stringify({
        message: 'Success!',
        user,
      }),
      { status: 200 }
    )
  }

  if (email && otp) {
    const userService = new UserService()
    const { user, isVerified } = await userService.verifyOTP(email, otp)

    if (!isVerified) {
      return new Response(
        JSON.stringify({
          otp: 'Invalid OTP',
        }),
        { status: 400 }
      )
    }

    const sessionService = new SessionService()
    const session = await sessionService.createSession(user._id)

    await userService.updateLastLoggedIn(user._id)

    return new Response(
      JSON.stringify({
        message: 'Success!',
        user,
        session,
      }),
      {
        status: 200,
      }
    )
  }
}


const verifyHCaptcha = async (token: string) => {
  const secret = process.env.HCAPTCHA_SECRET_KEY
  const response = await fetch('https://hcaptcha.com/siteverify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `response=${token}&secret=${secret}`,
  })

  const data = await response.json()

  return data.success
}
