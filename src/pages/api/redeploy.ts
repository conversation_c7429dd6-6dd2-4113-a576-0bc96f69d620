import type { APIRoute } from 'astro'
import db from '../../lib/db/db'
import { UserService, SessionService } from '../../lib/services'
import { GITHUB_OWNER, GITHUB_REPO, GITHUB_TOKEN } from '../../server-constants'
export const prerender = false

export const POST: APIRoute = async ({ request, url }) => {
  const searchParams = url.searchParams
  const sessionId = searchParams.get('token')
  if (!sessionId) {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const sessionService = new SessionService()
  const userProfile =
    await sessionService.checkSessionIsActiveAndGetCurrentUser(sessionId)

  if (!userProfile || userProfile.accessLevel !== 'admin') {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const response = await sendRedeployRequestToGithub()
    .then(async (res) => {
      if (res.status !== 204) {
        return new Response(
          JSON.stringify({
            message: 'Error sending redeploy request',
            error: res,
          }),
          { status: 500 }
        )
      }

      return new Response(
        JSON.stringify({
          message: 'Redeploy request sent',
        }),
        { status: 200 }
      )
    })
    .catch((err) => {
      return new Response(
        JSON.stringify({
          message: 'Error sending redeploy request',
          error: err,
        }),
        { status: 500 }
      )
    })
  return response
}

const sendRedeployRequestToGithub = async () => {
  const repo = GITHUB_REPO
  const owner = GITHUB_OWNER
  const token = GITHUB_TOKEN
  const url = `https://api.github.com/repos/${owner}/${repo}/dispatches`

  const body = {
    event_type: 'redeploy',
  }

  return fetch(url, {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/vnd.github.v3+json',
    },
  })
}
