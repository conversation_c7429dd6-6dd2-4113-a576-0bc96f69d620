import type { APIRoute } from 'astro'
import db from '../../lib/db/db'
import { UserService, SessionService } from '../../lib/services'
import * as jose from 'jose'
export const prerender = false

export const GET: APIRoute = async ({ url }) => {
  const searchParams = url.searchParams
  const sessionId = searchParams.get('token')
  if (!sessionId) {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const sessionService = new SessionService()
  const userProfile =
    await sessionService.checkSessionIsActiveAndGetCurrentUser(sessionId)

  if (!userProfile || userProfile.accessLevel !== 'admin') {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const userService = new UserService()

  const users = await userService.getUsers({
    sort: { createdAt: -1 },
  })
  return new Response(
    JSON.stringify({
      message: 'Success!',
      ...users,
    }),
    { status: 200 }
  )
}

export const POST: APIRoute = async ({ request, url }) => {
  const searchParams = url.searchParams
  const sessionId = searchParams.get('token')
  if (!sessionId) {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const sessionService = new SessionService()
  const userProfile =
    await sessionService.checkSessionIsActiveAndGetCurrentUser(sessionId)

  if (!userProfile || userProfile.accessLevel !== 'admin') {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const requestBody = await request.json()

  const { fullname, email, accessLevel, vcType, seedType, _id, company, role } =
    requestBody

  if (!email || !accessLevel || !vcType) {
    return new Response(
      JSON.stringify({
        message: 'Missing required fields',
      }),
      { status: 400 }
    )
  }

  if (!_id) {
    // check if email already exists
    const userService = new UserService()

    const existingUser = await userService.getUserByEmail(email)

    if (existingUser && existingUser.length > 0) {
      return new Response(
        JSON.stringify({
          message: 'Email already exists',
        }),
        { status: 400 }
      )
    }

    // non-admin users can only create users with accessLevel: user
    if (userProfile.accessLevel !== 'admin' && accessLevel !== 'user') {
      return new Response(
        JSON.stringify({
          message: 'Unauthorized',
        }),
        { status: 401 }
      )
    }

    const newUser = await userService.createUser({
      company,
      role,
      fullname,
      email,
      accessLevel,
      vcType,
      seedType,
      createdBy: userProfile._id,
    })

    await userService.sendWelcomeEmail(email, vcType)

    return new Response(
      JSON.stringify({
        message: 'Success!',
        user: newUser,
      }),
      { status: 200 }
    )
  }

  const userService = new UserService()
  const existingUser = await userService.getUserById(_id)

  if (!existingUser) {
    return new Response(
      JSON.stringify({
        message: 'User not found',
      }),
      { status: 400 }
    )
  }

  // non-admin users can only create users with accessLevel: user
  if (userProfile.accessLevel !== 'admin' && accessLevel !== 'user') {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const updatedUser = await userService.updateUser(_id, {
    fullname,
    accessLevel,
    vcType,
    seedType,
    company,
    role,
  })

  return new Response(
    JSON.stringify({
      message: 'Success!',
      user: updatedUser,
    }),
    { status: 200 }
  )
}

export const DELETE: APIRoute = async ({ request, cookies, url }) => {
  const searchParams = url.searchParams
  const sessionId = searchParams.get('token')
  if (!sessionId) {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const sessionService = new SessionService()
  const userProfile =
    await sessionService.checkSessionIsActiveAndGetCurrentUser(sessionId)

  if (!userProfile || userProfile.accessLevel !== 'admin') {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const requestBody = await request.json()

  const { _id } = requestBody

  if (!_id) {
    return new Response(
      JSON.stringify({
        message: 'Missing required fields',
      }),
      { status: 400 }
    )
  }

  if (_id === userProfile._id) {
    return new Response(
      JSON.stringify({
        message: 'Deleting your own account is not allowed.',
      }),
      { status: 400 }
    )
  }

  const userService = new UserService()
  const existingUser = await userService.getUserById(_id)

  if (!existingUser) {
    return new Response(
      JSON.stringify({
        message: 'User not found',
      }),
      { status: 400 }
    )
  }

  const deletedUser = await userService.deleteUser(_id)

  return new Response(
    JSON.stringify({
      message: 'Success!',
      user: deletedUser,
    }),
    { status: 200 }
  )
}
