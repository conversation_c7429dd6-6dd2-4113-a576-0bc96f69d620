import type { APIRoute } from 'astro'
export const prerender = false

import unirest from 'unirest'
import * as cheerio from 'cheerio'

const NEWSCRE = 'https://www.cre.vc/news'

const getHTML = async (url) => {
  // fake the request as though it's coming from a browser
  const response = await unirest.get(url).headers({
    'User-Agent':
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 ' +
      '(KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
  })
  return response.body

  // const response = await unirest.get(url)
  // return response.body
}

export const GET: APIRoute = async ({ request }) => {
  const html = await getHTML(NEWSCRE)
  if (!html)
    return new Response(
      JSON.stringify({
        message: 'Error!',
        news: [],
      }),
      { status: 500 }
    )
  const chInstance = cheerio.load(html)

  const news = chInstance('.summary-item-has-thumbnail')
    .map((i, el) => {
      // find first a tag
      const a = chInstance(el).find('a').first()
      // title is the data-title attribute
      const title = a.attr('data-title')
      // link is the href attribute
      const link = a.attr('href')

      // find first img tag
      const img = chInstance(el).find('img').first()

      // imgSrc is the data-src attribute
      const imgSrc = img.attr('data-src')
      // find the first time tag
      const time = chInstance(el).find('time').first()
      return {
        title,
        link: link,
        imgSrc: imgSrc,
        time: time.text(),
      }
    })
    .get()

    // remove duplicates
    .filter((item, index, self) => self.findIndex((t) => t.title === item.title) === index)

  return new Response(
    JSON.stringify({
      message: 'Success!',
      news: news,
      // raw: html,
    }),
    { status: 200 }
  )
}
