import type { APIRoute } from 'astro'
import db from '../../lib/db/db'
import { UserService, SessionService } from '../../lib/services'
import * as jose from 'jose'
import { JoinSecretsService } from '../../lib/services/join-secrets'
export const prerender = false

export const GET: APIRoute = async ({ url }) => {
  const searchParams = url.searchParams
  const sessionId = searchParams.get('token')
  if (!sessionId) {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const sessionService = new SessionService()
  const userProfile =
    await sessionService.checkSessionIsActiveAndGetCurrentUser(sessionId)

  if (!userProfile) {
    return new Response(
      JSON.stringify({
        message: 'Unauthorized',
      }),
      { status: 401 }
    )
  }

  const secrets = new JoinSecretsService()

  const secretProfile = await secrets.createSecretsUser(userProfile)

  if (!secretProfile) {
    return new Response(
      JSON.stringify({
        message: 'Something went Wrong',
      }),
      { status: 500 }
    )
  }

  const sign_in_url = await secrets.getSignInURLDirectly(secretProfile.secretID)

  return new Response(
    JSON.stringify({
      data: sign_in_url,
    }),
    { status: 200 }
  )
}
