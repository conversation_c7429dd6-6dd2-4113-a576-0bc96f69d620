---
import AuthenticationChecker from '../components/svelte/AuthenticationChecker.svelte'
import Login from '../components/Login.astro'
import Layout from '../layouts/Layout.astro'

import TableOfContents from '../components/svelte/TableOfContents.svelte'
import {
  getAllCategoriesForFounder,
  getAllCategoriesForLP,
  getAllPosts,
} from '../lib/notion/client'
import Home from '../components/svelte/Home.svelte'
import NewsDisplay from '../components/svelte/NewsDisplay.svelte'
import Copyright from '../components/Copyright.astro'
import Menu from '../components/svelte/Menu.svelte'
import SearchBox from '../components/svelte/SearchBox.svelte'
import SlackButton from '../components/svelte/SB.svelte'

const [founderCategories, lpCategories, allPosts] = await Promise.all([
  getAllCategoriesForFounder(),
  getAllCategoriesForLP(),
  getAllPosts(),
])
---

<AuthenticationChecker client:load>
  <Layout
    slot="not-allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
  >
    <div class="login-container">
      <div class="login">
        <Login />
      </div>
    </div>
    <Copyright slot="footer" />
  </Layout>

  <Layout
    slot="allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
    search={true}
  >
    <div class="header" slot="header">
      <div class="search">
        <SearchBox client:load />
      </div>

      <div class="back-home">
        <a href="https://cre.vc"> Back to cre.vc</a>

        <SlackButton client:load />
      </div>

      <div class="menu">
        <Menu client:only />
      </div>
    </div>
    <TableOfContents
      client:only
      slot="sidebarLeft"
      lpCategories={lpCategories}
      founderCategories={founderCategories}
      allPosts={allPosts}
    />

    <div class="paper-weight" slot="sidebarLeft"></div>

    <Home
      client:only
      lpCategories={lpCategories}
      founderCategories={founderCategories}
      allPosts={allPosts}
    />

    <NewsDisplay client:idle slot="sidebarRight" />
    <Copyright slot="footer" />
  </Layout>
</AuthenticationChecker>

<style>
  .login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  .login {
    max-width: 600px;
  }
</style>
