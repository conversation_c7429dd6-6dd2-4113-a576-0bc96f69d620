---
import AuthenticationChecker from '../../components/svelte/AuthenticationChecker.svelte'
import NotAllowed from '../../components/NotAllowed.astro'
import Layout from '../../layouts/Layout.astro'
import TableOfContents from '../../components/svelte/TableOfContents.svelte'
import PostTitle from '../../components/PostTitle.astro'
import PostBody from '../../components/PostBody.astro'
import SearchBox from '../../components/svelte/SearchBox.svelte'
import Menu from '../../components/svelte/Menu.svelte'
import {
  getAllCategoriesForFounder,
  getAllCategoriesForLP,
  getAllPosts,
  getPostByPageId,
  getAllBlocksByBlockId,
  getBlock,
  downloadFile,
} from '../../lib/notion/client'
import NewsDisplay from '../../components/svelte/NewsDisplay.svelte'
import { type Post } from '../../lib/interfaces'
import { extractTargetBlocks } from '../../lib/blog-helpers'
import Copyright from '../../components/Copyright.astro'
import ResourcePagination from '../../components/svelte/ResourcePagination.svelte'
import SlackButton from '../../components/svelte/SB.svelte'

const [founderCategories, lpCategories, allPosts] = await Promise.all([
  getAllCategoriesForFounder(),
  getAllCategoriesForLP(),
  getAllPosts(),
])

export async function getStaticPaths() {
  const posts = await getAllPosts()
  return posts.map((post: Post) => ({
    params: { slug: post.PageId },
  }))
}

const { slug } = Astro.params

const post = await getPostByPageId(slug)

const [blocks] = await Promise.all([getAllBlocksByBlockId(post?.PageId)])

const fileAtacchedBlocks = extractTargetBlocks('image', blocks)
  .concat(extractTargetBlocks('file', blocks))
  .filter((block) => {
    if (!block) {
      return false
    }
    const imageOrFile = block.Image || block.File
    return imageOrFile && imageOrFile.File && imageOrFile.File.Url
  })

// Download files

await Promise.all(
  fileAtacchedBlocks
    .map(async (block) => {
      const expiryTime = (block?.Image || block?.File).File.ExpiryTime
      if (Date.parse(expiryTime) > Date.now()) {
        return Promise.resolve(block)
      }
      return getBlock(block.Id)
    })
    .map((promise) =>
      promise.then((block) => {
        let url!: URL
        try {
          url = new URL((block?.Image || block?.File)?.File.Url)
        } catch (err) {
          console.log('Invalid file URL')
          return Promise.reject()
        }
        return Promise.resolve(url)
      })
    )
    .map((promise) => promise.then(downloadFile))
)

// const generatePrev = () => {
//   if (!post) {
//     return null
//   }

//   const allPostsForCategory = allPosts.filter(
//     (_post) => _post.Category.id === post.Category.id
//   )

//   const allPostAllowed

//   console.log('allPostsForCategory', allPostsForCategory)
//   return allPostsForCategory
// }
// console.log(generatePrev())
---

<AuthenticationChecker client:load post={post}>
  <Layout
    slot="not-allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
  >
    <NotAllowed />
    <Copyright slot="footer" />
  </Layout>

  <Layout
    slot="allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
    search={true}
  >
    <TableOfContents
      client:only
      slot="sidebarLeft"
      lpCategories={lpCategories}
      founderCategories={founderCategories}
      allPosts={allPosts}
    />
    <div class="header" slot="header">
      <div class="search">
        <SearchBox client:load />
      </div>

      <div class="back-home">
        <a href="https://cre.vc"> Back to cre.vc</a>
        <SlackButton client:load />
      </div>

      <div class="menu">
        <Menu client:only />
      </div>
    </div>

    <div class="post">
      {
        post && (
          <>
            <PostTitle post={post} enableLink={false} />
            <PostBody blocks={blocks} />
          </>
        )
      }
    </div>
    <NewsDisplay client:idle slot="sidebarRight" />
    <ResourcePagination client:load slot="pageFooter" {allPosts} {post} />
    <Copyright slot="footer" />
  </Layout>
</AuthenticationChecker>

<style>
  .post {
    margin: 1rem auto;
    /* max-width: 680px; */
    padding: 0 1.5rem;
  }
</style>
