---
export const prerender = false
import Copyright from '../components/Copyright.astro'
import NotAllowed from '../components/NotAllowed.astro'
import AuthenticationChecker from '../components/svelte/AuthenticationChecker.svelte'
import UserManagement from '../components/svelte/modules/UserManagement.svelte'
import Layout from '../layouts/Layout.astro'
---

<AuthenticationChecker client:load>
  <Layout
    slot="not-allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
  >
    <NotAllowed />
    <Copyright slot="footer" />
  </Layout>

  <Layout
    slot="allowed"
    title="CRE Resource Center"
    description="The CRE Resource Center is a collection of resources for the CRE community. It includes a list of CRE-related projects, a list of CRE-related papers, and a list of CRE-related events."
    path="/"
    search={true}
  >
    <UserManagement client:load />
    <Copyright slot="footer" />
  </Layout>
</AuthenticationChecker>

<style></style>
