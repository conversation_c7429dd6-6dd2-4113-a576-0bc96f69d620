---
import { PUBLIC_GA_TRACKING_ID, ENABLE_LIGHTBOX } from '../server-constants.ts'
import { getNavLink, getStaticFilePath } from '../lib/blog-helpers.ts'
import '../styles/syntax-coloring.css'
import '../font/stylesheet.css'
import GoogleAnalytics from '../components/GoogleAnalytics.astro'
import ErrorShow from '../components/svelte/modules/ErrorShow.svelte'
import Drawer from '../components/svelte/modules/drawer/Drawer.svelte'

import {
  getAllCategoriesForFounder,
  getAllCategoriesForLP,
  getAllPosts,
} from '../lib/notion/client'
import SearchModal from '../components/svelte/SearchModal.svelte'

export interface Props {
  title: string
  description: string
  path: string
  ogImage?: string
  search?: boolean
}

const {
  title = '',
  description = '',
  path = '/',
  ogImage = '',
  search = false,
} = Astro.props

const siteTitle = title ? `${title} - CRE` : 'CRE Resource Library'
const siteDescription = description
  ? description
  : 'This comprehensive repository houses diverse tools, insights, and educational materials, designed to equip you with the latest industry trends, best practices, and strategic guidance. '
const siteURL = new URL(getNavLink(path), Astro.site).toString()
const siteOGImage = new URL(getStaticFilePath('/logo-high.png'), Astro.site)

const [founderCategories, lpCategories, allPosts] = await Promise.all([
  getAllCategoriesForFounder(),
  getAllCategoriesForLP(),
  getAllPosts(),
])
---

<!doctype html>
<html lang="en" prefix="og: https://ogp.me/ns#">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="max-image-preview:large" />
    <meta charset="UTF-8" />
    <meta name="generator" content={Astro.generator} />
    <title>{siteTitle}</title>
    <meta name="description" content={siteDescription} />
    <link rel="canonical" href={siteURL} />
    <meta property="og:url" content={siteURL} />
    <meta property="og:title" content={siteTitle} />
    <meta property="og:description" content={siteDescription} />
    <meta property="og:site_name" content="CRE" />
    <meta property="og:image" content={ogImage || siteOGImage} />
    <meta name="twitter:title" content={siteTitle} />
    <meta name="twitter:description" content={siteDescription} />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content={ogImage || siteOGImage} />

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <meta
      name="google-site-verification"
      content="iSWvh6r3zkBu07Sq0MEJY7oNcWZTtG8yPUE15NDo6o8"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.css"
      integrity="sha384-vKruj+a13U8yHIkAyGgK1J3ArTLzrFGBbBc0tDp4ad/EyewESeXE/Iv67Aj8gKZ0"
      crossorigin="anonymous"
    />

    <!-- <link rel="stylesheet" href="/font/stylesheet.css" /> -->
    <!-- Google Tag Manager -->
    <script>
      ;(function (w, d, s, l, i) {
        w[l] = w[l] || []
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : ''
        j.async = true
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl
        f.parentNode.insertBefore(j, f)
      })(window, document, 'script', 'dataLayer', 'GTM-KDBRX7RD')
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body>
    <GoogleAnalytics trackingId={PUBLIC_GA_TRACKING_ID} />

    <div id="app-shell">
      <!-- Slot: Header -->
      <header id="shell-header">
        <div class="logo">
          <a href="/">
            <img src="/logo-high.png" alt="Astro logo" />
          </a>
          <h1 class="special">
            <a href="/"> Resource Center</a>
          </h1>
        </div>

        <slot name="header" />
      </header>

      <!-- Content Area -->
      <div class="content-area">
        <!-- Slot: Sidebar (left) -->
        <aside id="sidebar-left">
          <slot name="sidebarLeft" />
        </aside>

        <!-- Page -->
        <div id="page">
          <!-- Slot: Page Header -->
          <header id="page-header">
            <slot name="pageHeader" />
          </header>

          <!-- Slot: Page Content (default) -->
          <main id="page-content">
            <slot />
            <Drawer
              founderCategories={founderCategories}
              allPosts={allPosts}
              lpCategories={lpCategories}
              menu={true}
              client:only
            />
          </main>

          <!-- Slot: Page Footer -->
          <footer id="page-footer">
            <slot name="pageFooter" />
          </footer>
        </div>

        <!-- Slot: Sidebar (right) -->
        <aside id="sidebar-right">
          <slot name="sidebarRight" />
        </aside>
      </div>

      <!-- Slot: footer -->
      <footer id="shell-footer">
        <slot name="footer" />
      </footer>
    </div>

    <SearchModal client:only {allPosts} />
    {
      ENABLE_LIGHTBOX && (
        <script src={getStaticFilePath('/scripts/fslightbox.js')} />
      )
    }

    <ErrorShow client:load />

    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-KDBRX7RD"
        height="0"
        width="0"
        style="display:none;visibility:hidden"></iframe></noscript
    >
    <!-- End Google Tag Manager (noscript) -->
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-6NDPZBQCXX"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-6NDPZBQCXX');
</script>
  </body>
</html>

<style>
  #app-shell {
    display: flex;
    overflow: hidden;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }
  #shell-header {
    flex: none;
    z-index: var(--z10);
    display: flex;
    box-shadow:
      0 1px 1px rgba(0, 0, 0, 0.12),
      0 2px 2px rgba(0, 0, 0, 0.12);
  }
  #shell-header .logo {
    display: flex;
    align-items: center;
    padding: var(--gap);
  }

  @media (min-width: 130px) {
    #shell-header .logo {
      width: 350px;
    }
  }

  #shell-header .logo img {
    width: 100%;
    max-width: 40px;
  }

  .content-area {
    flex: 1 1 auto;
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
  #sidebar-left,
  #sidebar-right {
    overflow-y: auto;
    overflow-x: hidden;
    flex: none;
    width: auto;
  }
  #page {
    display: flex;
    overflow-x: hidden;
    position: relative;
    flex-direction: column;
    flex: 1 1 0%;
  }

  #page-header {
    flex: none;
    position: sticky;
    top: 0;
    z-index: var(--z10);
  }
  #page-content {
    flex: 1 1 auto;
  }
  #page-footer {
    flex: none;
  }
  #shell-footer {
    flex: none;
  }

  .special {
    margin-left: 0.5rem;
    text-transform: capitalize;
    font-size: 1rem;
    font-weight: 900;
    font-family: var(--font-family-inter);
  }

  @media screen and (min-width: 600px) {
    .special {
      font-size: 2rem;
    }
  }
</style>

<style is:global>
  :root {
    --gap-quarter: 0.25rem;
    --gap-half: 0.5rem;
    --gap: 1rem;
    --gap-double: 2rem;
    --bg: #ffffff;
    --fg: #001f3f;
    --accents-1: rgb(17, 24, 39);
    --accents-2: #666;
    --accents-3: #666;
    --geist-foreground: #253775;
    --geist-success: #690;
    --error: #c00;

    --radius: 4px;

    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
      'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
      'Helvetica Neue', sans-serif;
    --font-mono: SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono',
      Courier, monospace;
    --font-family: fsp, 'Mulish', sans-serif !important;
    --font-family-inter: fsp, 'Inter', 'Mulish', sans-serif !important;
    --z-1: -1;
    --z-0: 0;
    --z1: 1;
    --z2: 2;
    --z5: 5;
    --z10: 10;
    --z20: 20;
    --z50: 50;
    --z100: 100;
  }

  * {
    box-sizing: border-box;
    word-break: break-word;
  }

  html,
  body {
    padding: 0;
    margin: 0;
    font-size: 16px;
  }

  body {
    height: 100vh;
    background: var(--bg);
    color: var(--accents-1);
    font-family: var(--font-family);

    /*background-image: radial-gradient(#ddd 1px, transparent 1px),
      radial-gradient(#ddd 1px, transparent 1px);*/
    background-position:
      0 0,
      25px 25px;
    background-attachment: fixed;
    background-size: 50px 50px;

    /* Hack */
    overflow-x: hidden;

    -webkit-font-smoothing: antialiased;
    -webkit-text-size-adjust: 100%;
  }

  figure {
    margin: 0;
    font-size: 0.85rem;
    color: #999;
    line-height: 1.8rem;
  }

  a {
    color: var(--accents-1);
    text-decoration: none;
  }
  a:hover {
    color: var(--accents-1);
  }

  mark {
    padding: var(--gap-quarter);
    border-radius: var(--radius);
    background: rgba(247, 212, 255, 0.8);
  }

  h1 {
    margin: 0;
    color: var(--accents-1);
    font-size: 2rem;
    font-weight: 700;
    font-family: var(--font-family-inter);
  }

  @media (max-width: 640px) {
    h1 {
      font-size: 1.6rem;
    }
  }

  ol {
    font-weight: 400;
    font-size: 0.9rem;
    line-height: 1.8rem;
  }
  p,
  ul {
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.8rem;
  }
  ul,
  ol {
    margin: 0;
    padding-inline-start: 1.5rem;
  }
  pre {
    margin: 0;
    white-space: pre;
    tab-size: 2;
  }
  pre :global(code) {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }

  hr {
    display: block;
    height: 1px;
    border: 0;
    margin: 0.3rem 0;
    background-color: #333;
  }

  code {
    font-size: 0.9rem;
    background: rgba(135, 131, 120, 0.15);
    color: #eb5757;
    padding: 0.25rem;
    border-radius: var(--radius);
    font-family: var(--font-mono);
  }

  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    border-collapse: collapse;
  }

  table tr {
    vertical-align: top;
  }

  table th {
    font-weight: 600;
    background-color: #f4f4f4;
  }

  table td,
  table th {
    font-size: 0.9rem;
    border: 1px solid #e4e4e4;
    padding: 0.5rem 1rem;
    text-align: left;
    line-height: 1.2rem;
  }
  table td::after {
    content: '';
    display: inline-block;
    min-height: 0.9rem;
  }

  button {
    background: none;
    border: 0;
    cursor: pointer;
    color: inherit;
  }

  /* animations */

  /* shaking */

  .shaking {
    animation: shake 0.5s;
  }

  @keyframes shake {
    10%,
    90% {
      transform: translate3d(-1px, 0, 0);
    }

    20%,
    80% {
      transform: translate3d(2px, 0, 0);
    }

    30%,
    50%,
    70% {
      transform: translate3d(-4px, 0, 0);
    }

    40%,
    60% {
      transform: translate3d(4px, 0, 0);
    }
  }

  #sidebar-left,
  #sidebar-right,
  #page-footer,
  #shell-footer {
    background-color: rgb(250 250 250/1);
  }

  #sidebar-left {
    background-image: radial-gradient(#e5e7eb 1px, transparent 0);
    background-size: 16px 16px;
  }

  .paper-weight {
    width: 100%;
    width: 300px;
    display: none;
  }

  .user-av {
    display: none;
  }
  .hamburger-menu {
    display: block;
    width: 1.5rem;
  }

  @media (min-width: 1000px) {
    .paper-weight {
      display: block;
    }
    .user-av {
      display: block;
    }
    .hamburger-menu {
      display: none;
    }
  }
  .header {
    display: flex;
    flex: 1;
    width: 100%;
  }

  .search {
    margin: auto;
    display: none;
  }

  .back-home {
    margin: auto;
    margin-left: auto;
    margin-right: 1rem;

    display: none;
  }

  @media (min-width: 901px) {
    .search {
      display: block;
      width: 100%;
      max-width: 900px;
      margin: auto 0;
    }
    .back-home {
      display: flex;
      gap: 1rem;
      justify-content: center;
      align-items: center;
    }
    .back-home a {
      display: block;
      padding: 10px;
    }
  }

  @media (min-width: 1075px) {
    .back-home a {
      background-color: #141c4c;
      color: #fff;
      text-align: center;
      text-decoration: none;
      border-radius: 5px;
      font-weight: 600;
    }
  }

  .menu {
    margin: auto;
    margin-right: 2rem;
  }

  .pagination {
    background-color: #fff;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pagination .left {
    text-align: left;
  }

  .pagination .right {
    margin-left: auto;
    text-align: right;
  }
</style>
