---
import * as interfaces from '../../lib/notion/interfaces'
import { filePath } from '../../lib/blog-helpers'
import Caption from './Caption.astro'

export interface Props {
  block: interfaces.Block
}

const { block } = Astro.props

let url: URL
try {
  url = new URL(block.File?.External?.Url || block.File?.File?.Url)
} catch (err) {
  console.error(`Invalid file URL. error: ${err}`)
}

const filename = decodeURIComponent(url.pathname.split('/').slice(-1)[0])
---

<div class="file">
  <div>
    {
      url && (
        <a
          href={block.File.External ? url.String() : filePath(url)}
          target="_blank"
          rel="noopener noreferrer"
        >
          <img
            src="https://www.notion.so/icons/document_gray.svg"
            alt="File icon in a file block"
          />{' '}
          {filename}
        </a>
      )
    }
  </div>
  <Caption richTexts={block.File.Caption} />
</div>

<style>
  .file {
  }

  .file a {
    display: block;
    padding: 0.5rem 0.2rem 0.4rem;
    border-radius: var(--radius);
    color: var(--accents-1);
    font-weight: 500;
    line-height: 1.4rem;
  }
  .file a:hover {
    background-color: #eee;
  }

  .file a img {
    width: 1.3rem;
    height: 1.3rem;
    vertical-align: sub;
  }
</style>
