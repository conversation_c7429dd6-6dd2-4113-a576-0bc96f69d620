---
import * as interfaces from '../../lib/interfaces.ts'
import { snakeToKebab } from '../../lib/style-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'
import '../../styles/notion-color.css'

export interface Props {
  block: interfaces.Block
  headings: interfaces.Block[]
}

const { block, headings } = Astro.props
---

<ul>
  {
    block.ListItems.filter(
      (b: interfaces.Block) => b.Type === 'bulleted_list_item'
    ).map((b: interfaces.Block) => (
      <li class={snakeToKebab(b.BulletedListItem.Color)}>
        {b.BulletedListItem.RichTexts.map((richText: interfaces.RichText) => (
          <RichText richText={richText} />
        ))}
        {b.HasChildren && (
          <NotionBlocks
            blocks={b.BulletedListItem.Children}
            headings={headings}
          />
        )}
      </li>
    ))
  }
</ul>

<style>
  ul {
    font-size: 1rem;
  }
</style>
