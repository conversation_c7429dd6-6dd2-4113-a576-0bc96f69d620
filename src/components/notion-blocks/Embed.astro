---
import * as interfaces from '../../lib/interfaces.ts'
import {
  isTweetURL,
  isTikTokURL,
  isInstagramURL,
  isPinterestURL,
  isCodePenURL,
} from '../../lib/blog-helpers.ts'
import Bookmark from './Bookmark.astro'
import TweetEmbed from './TweetEmbed.astro'
import TikTokEmbed from './TikTokEmbed.astro'
import InstagramEmbed from './InstagramEmbed.astro'
import PinterestEmbed from './PinterestEmbed.astro'
import CodePenEmbed from './CodePenEmbed.astro'

export interface Props {
  block: interfaces.Block
  urlMap: { [key: string]: string }
}

const { block, urlMap } = Astro.props

let url: URL
try {
  url = new URL(block.Embed.Url)
} catch (err) {
  console.log(err)
}
---

{
  url ? (
    isTweetURL(url) ? (
      <TweetEmbed url={url} />
    ) : isTikTokURL(url) ? (
      <TikTokEmbed url={url} />
    ) : isInstagramURL(url) ? (
      <InstagramEmbed url={url} />
    ) : isPinterestURL(url) ? (
      <PinterestEmbed url={url} />
    ) : isCodePenURL(url) ? (
      <CodePenEmbed url={url} />
    ) : (
      <Bookmark block={block} urlMap={urlMap} />
    )
  ) : null
}
