---
import * as interfaces from '../../lib/interfaces.ts'
import { buildHeadingId } from '../../lib/blog-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'

export interface Props {
  block: interfaces.Block
  headings: interfaces.Block[]
}

const { block, headings } = Astro.props

const id = buildHeadingId(block.Heading3)
---

{
  block.Heading3.IsToggleable ? (
    <details class="toggle">
      <summary>
        <a href={`#${id}`} id={id}>
          <h5>
            {block.Heading3.RichTexts.map((richText: interfaces.RichText) => (
              <RichText richText={richText} />
            ))}
          </h5>
        </a>
      </summary>
      <div>
        {block.Heading3.Children && (
          <NotionBlocks blocks={block.Heading3.Children} headings={headings} />
        )}
      </div>
    </details>
  ) : (
    <a href={`#${id}`} id={id}>
      <h5>
        {block.Heading3.RichTexts.map((richText: interfaces.RichText) => (
          <RichText richText={richText} />
        ))}
      </h5>
    </a>
  )
}

<style>
  h5 {
    margin: 0.9em 0 0.3em;
    color: var(--accents-1);
    font-size: 1.25rem;
  }
  @media (max-width: 640px) {
    h5 {
      font-size: 1.1rem;
    }
  }

  .toggle {
    margin: 1.2rem 0 0;
  }
  @media (max-width: 640px) {
    .toggle {
      margin: 1.1rem 0 0;
    }
  }

  .toggle > summary {
    cursor: pointer;
  }

  .toggle > summary > a {
    display: inline;
  }

  .toggle > summary > a > h5 {
    display: inline;
  }

  .toggle > div {
    margin-left: 1em;
  }
</style>
