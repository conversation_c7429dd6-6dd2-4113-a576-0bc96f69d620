---
export interface Props {
  url: URL
}

const { url } = Astro.props

const postURL =
  url.hostname === 'x.com' || url.hostname === 'www.x.com'
    ? new URL(url.pathname, 'https://twitter.com')
    : url
---

<div class="tweet-embed">
  <blockquote class="twitter-tweet">
    <a href={postURL}></a>
  </blockquote>
</div>

<script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

<style>
  .tweet-embed {
    width: 100%;
    max-width: 640px;
    margin: auto;
  }
  .tweet-embed div:first-child div:first-child {
    margin: auto;
  }
</style>
