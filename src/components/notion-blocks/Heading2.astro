---
import * as interfaces from '../../lib/interfaces.ts'
import { buildHeadingId } from '../../lib/blog-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'

export interface Props {
  block: interfaces.Block
  headings: interfaces.Block[]
}

const { block, headings } = Astro.props

const id = buildHeadingId(block.Heading2)
---

{
  block.Heading2.IsToggleable ? (
    <details class="toggle">
      <summary>
        <a href={`#${id}`} id={id}>
          <h4>
            {block.Heading2.RichTexts.map((richText: interfaces.RichText) => (
              <RichText richText={richText} />
            ))}
          </h4>
        </a>
      </summary>
      <div>
        {block.Heading2.Children && (
          <NotionBlocks blocks={block.Heading2.Children} headings={headings} />
        )}
      </div>
    </details>
  ) : (
    <a href={`#${id}`} id={id}>
      <h4>
        {block.Heading2.RichTexts.map((richText: interfaces.RichText) => (
          <RichText richText={richText} />
        ))}
      </h4>
    </a>
  )
}

<style>
  h4 {
    margin: 1em 0 0.3em;
    color: var(--accents-1);
    font-size: 1.5rem;
  }
  @media (max-width: 640px) {
    h4 {
      font-size: 1.2rem;
    }
  }

  .toggle {
    margin: 1.6rem 0 0;
  }
  @media (max-width: 640px) {
    .toggle {
      margin: 1.2rem 0 0;
    }
  }

  .toggle > summary {
    cursor: pointer;
  }

  .toggle > summary > a {
    display: inline;
  }

  .toggle > summary > a > h4 {
    display: inline;
  }

  .toggle > div {
    margin-left: 1em;
  }
</style>
