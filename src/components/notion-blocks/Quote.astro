---
import * as interfaces from '../../lib/interfaces.ts'
import { snakeToKebab } from '../../lib/style-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'
import '../../styles/notion-color.css'

export interface Props {
  block: interfaces.Block
  headings: interfaces.Block[]
}

const { block, headings } = Astro.props
---

<blockquote class={snakeToKebab(block.Quote.Color)}>
  {
    block.Quote.RichTexts.map((richText: interfaces.RichText) => (
      <RichText richText={richText} />
    ))
  }
  {
    block.Quote.Children && (
      <NotionBlocks blocks={block.Quote.Children} headings={headings} />
    )
  }
</blockquote>

<style>
  blockquote {
    margin: 0.6rem 0;
    padding: 0 0.9rem;
    border-left: 3px solid var(--accents-1);
    font-size: 1rem;
    line-height: 1.8rem;
  }
</style>
