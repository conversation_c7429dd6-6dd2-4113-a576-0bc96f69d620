---
import * as interfaces from '../../lib/interfaces.ts'
import { snakeToKebab } from '../../lib/style-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'
import '../../styles/notion-color.css'

export interface Props {
  block: interfaces.Block
  headings: interfaces.Block[]
}

const { block, headings } = Astro.props
---

<details class={`toggle ${snakeToKebab(block.Toggle.Color)}`}>
  <summary>
    {
      block.Toggle.RichTexts.map((richText: interfaces.RichText) => (
        <RichText richText={richText} />
      ))
    }
  </summary>
  <div>
    <NotionBlocks blocks={block.Toggle.Children} headings={headings} />
  </div>
</details>

<style>
  .toggle {
    padding: 0.4rem;
  }

  .toggle > summary {
    cursor: pointer;
  }

  .toggle > summary > a {
    display: inline;
  }

  .toggle > div {
    margin-left: 1em;
  }
</style>
