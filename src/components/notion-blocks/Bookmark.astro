---
import createMetascraper from 'metascraper'
import metascraperDescription from 'metascraper-description'
import metascraperImage from 'metascraper-image'
import metascraperTitle from 'metascraper-title'
import { isAmazonURL } from '../../lib/blog-helpers.ts'
import * as interfaces from '../../lib/interfaces.ts'

export interface Props {
  block: interfaces.Block
  urlMap: { [key: string]: string }
}

const { block, urlMap } = Astro.props

const urlString = (block.Bookmark || block.LinkPreview || block.Embed).Url

let url: URL
let metadata: Record<string, string>
try {
  url = new URL(urlString)

  if (!isAmazonURL(url)) {
    const metascraper = createMetascraper([
      metascraperDescription(),
      metascraperImage(),
      metascraperTitle(),
    ])
    const html = urlMap[url.toString()]
    if (html) {
      metadata = await metascraper({ html, url })
    }
  }
} catch (err) {
  console.log(err)
}
---

{
  url && (
    <>
      {isAmazonURL(url) ? (
        <div class="no-metadata">
          <a target="_blank" rel="noopener noreferrer" href={url.toString()}>
            {url.toString()}
          </a>
        </div>
      ) : (
        <div class="bookmark">
          <a href={url.toString()} target="_blank" rel="noopener noreferrer">
            <div>
              <div>{metadata && metadata.title}</div>
              <div>{metadata && metadata.description}</div>
              <div>
                <div>
                  <img
                    src={`https://www.google.com/s2/favicons?domain=${url.hostname}`}
                    alt="Favicon of the bookmark site"
                    loading="lazy"
                  />
                </div>
                <div>{url.origin}</div>
              </div>
            </div>
            <div>
              {metadata && metadata.image && (
                <img
                  src={metadata.image}
                  alt="Site image of the bookmark"
                  loading="lazy"
                />
              )}
            </div>
          </a>
        </div>
      )}
    </>
  )
}

<style>
  .no-metadata > a {
    border-bottom: 0.05em solid;
    border-color: var(--anchor-border);
    opacity: 0.7;
  }
  .bookmark {
    display: flex;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    font-size: 0.9rem;
  }
  .bookmark > a {
    width: 100%;
    box-sizing: border-box;
    text-decoration: none;
    border: 1px solid rgba(55, 53, 47, 0.16);
    border-radius: 3px;
    display: flex;
    overflow: hidden;
    user-select: none;
  }
  .bookmark > a > div:first-child {
    flex: 4 1 180px;
    padding: 12px 14px 14px;
    overflow: hidden;
    text-align: left;
    color: var(--accents-1);
  }
  .bookmark > a > div:first-child > div:first-child {
    width: 120px;
    min-width: 100%;
    font-size: 14px;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 24px;
    margin-bottom: 2px;
  }
  .bookmark > a > div:first-child > div:nth-child(2) {
    font-size: 12px;
    line-height: 16px;
    opacity: 0.8;
    height: 32px;
    overflow: hidden;
  }
  .bookmark > a > div:first-child > div:last-child {
    display: flex;
    margin-top: 6px;
  }
  .bookmark > a > div:first-child > div:last-child > div:first-child {
    width: 16px;
    height: 16px;
    min-width: 16px;
    margin-right: 6px;
  }
  .bookmark > a > div:first-child > div:last-child > div:first-child > img {
    max-width: 100%;
    display: inline-block;
  }
  .bookmark > a > div:first-child > div:last-child > div:last-child {
    font-size: 12px;
    line-height: 16px;
    color: var(--accents-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .bookmark > a > div:last-child {
    flex: 1 1 180px;
    position: relative;
  }
  @media (max-width: 640px) {
    .bookmark > a > div:last-child {
      display: none;
    }
  }
  .bookmark > a > div:last-child > img {
    position: absolute !important;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
