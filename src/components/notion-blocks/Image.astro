---
import { ENABLE_LIGHTBOX } from '../../server-constants.ts'
import * as interfaces from '../../lib/notion/interfaces'
import { filePath } from '../../lib/blog-helpers'
import Caption from './Caption.astro'

export interface Props {
  block: interfaces.Block
}

const { block } = Astro.props

let image = ''
if (block.Image.External) {
  image = block.Image.External.Url
} else if (block.Image.File) {
  image = filePath(new URL(block.Image.File.Url))
}
---

<figure class="image">
  {
    image && (
      <div>
        <div>
          {ENABLE_LIGHTBOX ? (
            <a data-fslightbox href={image} data-type="image">
              <img src={image} alt="Image in a image block" loading="lazy" />
            </a>
          ) : (
            <img src={image} alt="Image in a image block" loading="lazy" />
          )}
        </div>
        <Caption richTexts={block.Image.Caption} />
      </div>
    )
  }
</figure>

<style>
  .image {
    display: flex;
    margin: 0.2rem auto 0;
    max-width: 400px;
  }
  .image > div {
    margin: 0 auto;
  }
  .image > div > div {
  }
  .image > div > div img {
    display: block;
    max-width: 100%;
  }
</style>
