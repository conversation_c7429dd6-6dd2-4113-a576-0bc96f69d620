---
import * as interfaces from '../../lib/interfaces.ts'
import { buildHeadingId } from '../../lib/blog-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'

export interface Props {
  block: interfaces.Block
  headings: interfaces.Block[]
}

const { block, headings } = Astro.props

const id = buildHeadingId(block.Heading1)
---

{
  block.Heading1.IsToggleable ? (
    <details class="toggle">
      <summary>
        <a href={`#${id}`} id={id}>
          <h3>
            {block.Heading1.RichTexts.map((richText: interfaces.RichText) => (
              <RichText richText={richText} />
            ))}
          </h3>
        </a>
      </summary>
      <div>
        {block.Heading1.Children && (
          <NotionBlocks blocks={block.Heading1.Children} headings={headings} />
        )}
      </div>
    </details>
  ) : (
    <a href={`#${id}`} id={id}>
      <h3>
        {block.Heading1.RichTexts.map((richText: interfaces.RichText) => (
          <RichText richText={richText} />
        ))}
      </h3>
    </a>
  )
}

<style>
  h3 {
    margin: 1.1em 0 0.3em;
    color: var(--accents-1);
    font-size: 1.8rem;
  }
  @media (max-width: 640px) {
    h3 {
      font-size: 1.3rem;
    }
  }

  .toggle {
    margin: 2rem 0 0;
  }
  @media (max-width: 640px) {
    .toggle {
      margin: 1.4rem 0 0;
    }
  }

  .toggle > summary {
    cursor: pointer;
  }

  .toggle > summary > a {
    display: inline;
  }

  .toggle > summary > a > h3 {
    display: inline;
  }

  .toggle > div {
    margin-left: 1em;
  }
</style>
