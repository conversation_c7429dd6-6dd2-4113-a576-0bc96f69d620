---
import * as interfaces from '../../lib/interfaces.ts'
import { snakeToKebab } from '../../lib/style-helpers.ts'
import RichText from './RichText.astro'
import NotionBlocks from '../NotionBlocks.astro'
import '../../styles/notion-color.css'

export interface Props {
  block: interfaces.Block
  level: number
  headings: interfaces.Block[]
}

const { block, level, headings } = Astro.props

const listTypes = ['i', '1', 'a']
---

<ol type={listTypes[level % 3]}>
  {
    block.ListItems.filter(
      (b: interfaces.Block) => b.Type === 'numbered_list_item'
    ).map((b: interfaces.Block) => (
      <li class={snakeToKebab(b.NumberedListItem.Color)}>
        {b.NumberedListItem.RichTexts.map((richText: interfaces.RichText) => (
          <RichText richText={richText} />
        ))}
        {b.Has<PERSON><PERSON>n && (
          <NotionBlocks
            blocks={b.NumberedListItem.Children}
            level={level + 1}
            headings={headings}
          />
        )}
      </li>
    ))
  }
</ol>

<style>
  ol {
    font-size: 1rem;
  }
</style>
