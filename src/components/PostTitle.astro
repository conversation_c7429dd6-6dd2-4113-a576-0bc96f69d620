---
import { type Post } from '../lib/interfaces.ts'
import { getPostLink } from '../lib/blog-helpers.ts'
import twemoji from 'twemoji'

export interface Props {
  post: Post
  enableLink: boolean
}

const { post, enableLink = true } = Astro.props

let title = post.Title

const emojiToTwiemojiUrl = (emoji) => {
  if (!emoji) return ''
  const img = twemoji.parse(emoji, {
    folder: 'svg',
    ext: '.svg',
  })
  if (!img) return ''
  const srcRegex = /<img[^>]+src="([^"]+)"/

  const match = img.match(srcRegex)
  if (!match) return ''
  return match[1]
}
---

<h2 class="post-title">
  {
    enableLink ? (
      <a href={getPostLink(post.PageId)}>
        {post.Icon && post.Icon.Type === 'emoji' ? (
          <>
            <img
              class="emoji"
              src={emojiToTwiemojiUrl(post.Icon.Emoji)}
              alt="Post title icon"
            />
            {title}
          </>
        ) : post.Icon && post.Icon.Type === 'external' ? (
          <>
            <img src={post.Icon.Url} alt="Post title icon" />
            {title}
          </>
        ) : (
          title
        )}
      </a>
    ) : (
      <>
        {post.Icon && post.Icon.Type === 'emoji' ? (
          <>
            <img
              class="emoji"
              src={emojiToTwiemojiUrl(post.Icon.Emoji)}
              alt="Post title icon"
            />
            {title}
          </>
        ) : post.Icon && post.Icon.Type === 'external' ? (
          <>
            <img src={post.Icon.Url} alt="Post title icon" />
            {title}
          </>
        ) : (
          title
        )}
      </>
    )
  }
</h2>

<style>
  .post-title {
    margin: 0;
    padding: 0.2rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--accents-1);
  }
  .post-title a {
    font-size: 1.6rem;
    color: inherit;
  }
  .post-title span,
  .post-title img {
    display: inline-block;
    margin-right: 0.2em;
  }
  .post-title span {
    font-size: 1.2em;
  }
  .post-title img {
    width: 1.3em;
    height: 1.3em;
    vertical-align: sub;
  }
  @media (max-width: 640px) {
    .post-title {
      font-size: 1.4rem;
    }
    .post-title a {
      font-size: 1.2rem;
    }
  }

  .emoji {
    height: 1em;
    width: 1em;
    margin: 0 0.05em 0 0.1em;
    vertical-align: -0.1em;
  }
</style>
