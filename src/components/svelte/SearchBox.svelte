<script>
  import twemoji from 'twemoji';
  import { showSearch } from './stores';
  const handleClick = () => {
    showSearch.set(true);
    const input = document.getElementById('search-input');
    if (input) input.focus();
  };

  const emojiToTwiemojiUrl = (emoji = '🔍') => {
    if (!emoji) return '';
    const img = twemoji.parse(emoji, {
      folder: 'svg',
      ext: '.svg',
    });
    if (!img) return '';
    const srcRegex = /<img[^>]+src="([^"]+)"/;

    const match = img.match(srcRegex);
    if (!match) return '';
    return match[1];
  };
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
<div
  class="search-box"
  on:click={handleClick}
  on:keypress={(e) => {
    if (e.key === 'Enter') {
      handleClick();
    }
  }}
>
  <img class="search-icon" src={emojiToTwiemojiUrl()} alt="" />
  <input
    class="search-input"
    type="text"
    placeholder="Search"
    on:click={handleClick}
  />
</div>

<style>
  .search-box {
    position: relative;
    display: flex;
    align-items: center;
    margin: 0 auto;
    margin-left: 5rem;
    padding: 5px 30px;
    max-width: 30vh;
    width: 100%;
    border: 1px solid #000;
    border-radius: 5px;
  }

  .search-icon {
    position: absolute;
    top: 50%;
    left: 10px;
    width: 1rem;
    transform: translateY(-50%);
    color: #999;
  }

  .search-input {
    width: 100%;
    padding-left: 30px;
    border: none;
    outline: none;
    font-size: 16px;
  }

  .search-input::placeholder {
    color: #000;
  }
</style>
