<script lang="ts">
  import { onMount } from 'svelte';
  import type { Post, SelectProperty } from '../../lib/interfaces';
  import { secretsHREF, userNanoStore } from './stores';
  import * as jose from 'jose';

  const pathJoin = (path: string, subPath: string) => {
    return (
      '/' +
      path
        .split('/')
        .concat(subPath.split('/'))
        .filter((p) => p)
        .join('/')
    );
  };

  const BASE_PATH = '';

  const filePath = (url: URL): string => {
    if (!url) {
      return '';
    }

    const [dir, filename] = url.pathname.split('/').slice(-2);
    return pathJoin(BASE_PATH, `/notion/${dir}/${filename}`);
  };

  const handleLogOut = () => {
    localStorage.removeItem('token');
    window.location.href = '/';
  };

  export let founderCategories: SelectProperty[];
  export let lpCategories: SelectProperty[];
  export let allPosts: Post[];
  export let slug: string | undefined = undefined;
  export let noShow: boolean = false;

  const companyColors = [
    // '#DBDBDB',
    '#141C4C',
  ];

  const randomColor = () => {
    return companyColors[Math.floor(Math.random() * companyColors.length)];
  };

  const detectVcType = () => {
    return $userNanoStore?.userType;
  };

  $: isLp = $userNanoStore?.userType === 'lp';
  const categoriesTagSet = new Map<string, Set<string>>();

  const sortOrder = [
    'Cap Table Management',
    'Cloud Credits',
    'Financial Management',
    'Employer of Record',
    'Legal and Compliance',
    'Educational',
  ];

  const buildSubsections = (
    type: 'founder' | 'lp',
    series: string,
    category: string
  ) => {
    const postsForType = allPosts.filter((post) => {
      return post.type === type;
    });

    if (type === 'lp') {
      return postsForType
        .filter((post) => {
          return post.Category.id === category;
        })
        .sort((a, b) => {
          if (a.Rank < b.Rank) {
            return -1;
          } else if (a.Rank === b.Rank) {
            return 0;
          }
          return 1;
        })
        .map((post) => {
          if (post.Tag) {
            if (categoriesTagSet.has(post.Category.id)) {
              categoriesTagSet.get(post.Category.id)?.add(post.Tag.name);
            } else {
              categoriesTagSet.set(post.Category.id, new Set([post.Tag.name]));
            }

            return {
              id: post.Tag.name,
              title: post.Tag.name,
              isTag: true,
              ...post,
            };
          }
          return {
            id: post.PageId,
            title: post.Title,
            ...post,
          };
        });
    }

    return postsForType
      .filter((post) => {
        return post.Category.id === category;
      })
      .sort((a, b) => {
        // if we have a Tag, we want to sort by the tag name alphabetically
        if (a.Tag && b.Tag) {
          const aIndex = sortOrder.indexOf(a.Tag.name);
          const bIndex = sortOrder.indexOf(b.Tag.name);

          if (aIndex !== -1 && bIndex !== -1) {
            if (aIndex < bIndex) {
              return -1;
            } else if (aIndex === bIndex) {
              return 0;
            }
            return 1;
          }

          if (a.Tag.name < b.Tag.name) {
            return -1;
          } else if (a.Tag.name === b.Tag.name) {
            return 0;
          }
          return 1;
        }

        if (a.Rank < b.Rank) {
          return -1;
        } else if (a.Rank === b.Rank) {
          return 0;
        }
        return 1;
      })
      .filter((post) => {
        if (!post?.For) {
          return true;
        }
        if (post?.For?.length === 0) {
          return true;
        }
        const _postSeries = post?.For?.map((s) => s.name.toLowerCase());

        return _postSeries.includes(series);
      })
      .map((post) => {
        if (post.Tag) {
          if (categoriesTagSet.has(post.Category.id)) {
            categoriesTagSet.get(post.Category.id)?.add(post.Tag.name);
          } else {
            categoriesTagSet.set(post.Category.id, new Set([post.Tag.name]));
          }

          return {
            id: post.Tag.name,
            title: post.Tag.name,
            isTag: true,
            ...post,
          };
        }

        return {
          id: post.PageId,
          title: post.Title,
          ...post,
        };
      });
  };

  $: allCategories = founderCategories.concat(lpCategories);

  $: slugCategory = allCategories.find((cat) => cat.id === slug);

  const buildContent = (isLp: boolean, isSlug = false) => {
    let categories = founderCategories;
    if (isLp) {
      categories = lpCategories;
    }

    if (!isSlug) {
      return categories.map((category) => {
        return {
          type: 'section',
          id: category.id,
          title: category.name,
          subsections:
            buildSubsections(
              isLp ? 'lp' : 'founder',
              $userNanoStore?.userSeries ?? '',
              category.id
            ) ?? [],
        };
      });
    } else {
      const category = categories.find((cat) => cat.id === slug);
      if (!category) {
        return [];
      }

      const subsections =
        buildSubsections(
          isLp ? 'lp' : 'founder',
          $userNanoStore?.userSeries ?? '',
          category.id
        ) ?? [];

      return subsections.map((sub) => {
        return {
          type: 'section',
          // id: sub.id,
          // title: sub.title,
          subsections: [],
          ...sub,
        };
      });
    }
  };

  $: contents = buildContent(isLp);

  $: slugContent = buildContent(isLp, true);

  $: secretsCategories = undefined;
  $: token = '';
  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };
  const secret = import.meta.env.PUBLIC_SECRET_KEY;
  const fetchLink = async () => {
    const _secret = new TextEncoder().encode(secret);
    const { payload } = await jose.jwtVerify<{
      userId: string;
      sessionId: string;
      isAdmin: boolean;
      usertype: string;
      userSeries: string;
      exp: number;
    }>(token, _secret);

    const { sessionId } = payload;
    token = sessionId;

    const res = await fetch(`/api/secrets?token=${token}`)
      .then((res) => res.json())
      .catch((e) => {
        // redirect to login
        handleLogOut();
      });
    if (res?.data) {
      secretsCategories = res.data;
      $secretsHREF = secretsCategories?.single_use_sign_in_url;
      // store the token
    }
  };

  onMount(async () => {
    // scroll home to top
    window.scrollTo(0, 0);
    token = retreiveToken();

    if (token) {
      fetchLink();
    }
  });

  $: generatePageTitle = () => {
    if (slug) {
      return slugCategory?.name;
    }

    if (!noShow) {
      if (detectVcType() === 'founder') {
        return 'Welcome to the CRE Resource Center! 👋';
      } else {
        return ' Welcome to the CRE LP Resource Center!';
      }
    }
  };
</script>

<svelte:head>
  <title>
    {generatePageTitle()}
  </title>
</svelte:head>

<div id="home" class="area">
  {#if !noShow}
    <div class="about">
      {#if detectVcType() === 'founder'}
        <h2>Welcome to the CRE Resource Center! 👋</h2>
        <!-- <p>
          The CRE Resource Center is your go-to hub, packed with tools,
          insights, and educational content to fuel your entrepreneurial
          journey. From navigating legal complexities to boosting market
          presence and scaling startups, find expert guidance here.
        </p>
        <p>
          Contact Us: <a href="mailto:<EMAIL>"><EMAIL></a>
        </p> -->
        <div class="choir">
          <p>
            The CRE Resource Center is your essential hub, filled with tools,
            insights, and learning materials to support your entrepreneurial
            journey. Here, you can find help with everything from legal issues
            to increasing your market visibility and growing your startup.
          </p>

          <p>
            On our partnerships page, we offer access to a handpicked selection
            of companies that are key to our ecosystem, including:
          </p>
          <ul>
            <li>Direct members of the CRE ecosystem</li>
            <li>Indirect members of the CRE ecosystem</li>
          </ul>

          <p>
            We've also arranged for discounts on services from these direct and
            indirect ecosystem partners, demonstrating our commitment to
            providing real value to our portfolio companies. This means you get
            the essential services you need at better rates.
          </p>
          <p>
            Got questions? Reach out to us at <a href="mailto:<EMAIL>"
              ><EMAIL></a
            >
          </p>
        </div>
      {/if}

      {#if detectVcType() === 'lp'}
        <h2>👋 Welcome to the CRE LP Resource Center!</h2>
        <p>
          Our meticulously curated platform empowers strategic decisions and
          offers exclusive insights tailored for our Limited Partners. Dive into
          a sophisticated repository crafted to enhance your investment journey
          and deepen your connection within our ecosystem.
        </p>

        <p>
          Contact Us: <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
      {/if}
    </div>
  {/if}

  {#if slug}
    <h3>{slugCategory?.name}</h3>
  {/if}

  <div class="card" class:hide={categoriesTagSet.get(slugCategory?.id ?? '')}>
    <div class="card-items">
      {#if !slug}
        {#each contents as item}
          <div class="item" style={`background-color: ${randomColor()};`}>
            {#if !item.title?.toLowerCase().startsWith('benefits')}
              <a
                href={`/section/${encodeURIComponent(item.id)}?section_name=${encodeURIComponent(item.title)}`}
                class="section-item">{item.title}</a
              >
            {:else}
              {#key secretsCategories}
                <a
                  target="_blank"
                  href={secretsCategories?.single_use_sign_in_url}
                  class="section-item">{item.title}</a
                >
              {/key}
            {/if}
          </div>
        {/each}
      {:else}
        {#each slugContent as item}
          {#if !item.isTag}
            <div
              class="item slug"
              style={`background-color: ${randomColor()};`}
            >
              <a
                href={`/resource/${item.id}?resource_name=${encodeURIComponent(item.title)}`}
                class="section-item slug-item">{item.title}</a
              >
            </div>
          {/if}
        {/each}
      {/if}
    </div>
  </div>

  <div class="tag-table-container">
    {#if slugCategory?.id && categoriesTagSet.get(slugCategory?.id)}
      <table class="benefits-table">
        <!-- <tr> </tr> -->
        {#each categoriesTagSet.get(slugCategory.id) as tag}
          {@const _itemsForTag = slugContent.filter(
            (item) => item?.Tag?.name === tag
          )}
          {@const itemsForTag = _itemsForTag.sort((a, b) => a?.Rank - b?.Rank)}
          <tr>
            <td class="bolded">{tag}</td>
            <td class="flex" class:grid={itemsForTag.length !== 3}>
              {#each itemsForTag as item}
                <a
                  title={item?.Active
                    ? (item?.Title ?? '')
                    : `${item?.Title ?? ''} Coming Soon`}
                  href={item?.Active
                    ? `/resource/${item?.PageId}?resource_name=${encodeURIComponent(item.title)}`
                    : '#'}
                  class="tag-item"
                  class:inactive={!item?.Active}
                >
                  {#if item?.FeaturedImage}
                    <div class="tag-image">
                      <img
                        src={filePath(new URL(item?.FeaturedImage?.Url)) ?? ''}
                        alt={item.Title}
                      />
                    </div>
                  {/if}

                  {#if !item?.Active}
                    <p class="late">Coming soon</p>
                  {/if}
                </a>
              {/each}
            </td>
          </tr>
        {/each}
      </table>

      {#if secretsCategories}
        <div class="wonder">
          <a
            target="_blank"
            class="slack"
            href={secretsCategories?.single_use_sign_in_url}
          >
            View more benefits
          </a>
        </div>
      {/if}
    {/if}
  </div>

  <!-- <div class="soon">
    {#if detectVcType() === 'founder'}
      <p>Coming Soon:</p>
      <div class="coming-soon">
        <div class="soon-item">
          <h4>People and Culture</h4>
        </div>
        <div class="soon-item">
          <h4>Growth and Strategy</h4>
        </div>
        <div class="soon-item">
          <h4>Community</h4>
        </div>
      </div>
    {/if}
  </div> -->
</div>

<style>
  h3 {
    text-decoration: underline;
    margin-top: 2rem;
  }

  .area {
    margin: auto;
  }

  @media screen and (width <= 900px) {
    .area {
      padding: 1rem;
    }
  }

  @media screen and (width >= 901px) {
    .area {
      padding: 0rem 1rem;
      margin-left: 5rem;
      max-width: 70vh;
    }
  }

  @media screen and (width <= 800px) {
    p {
      font-size: 0.8rem;
      font-weight: 300;
    }
  }

  .card {
    margin-top: 2rem;
  }

  .card-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    width: 100%;
    gap: 1rem;
  }

  @media screen and (width >= 901px) {
    .card-items {
      grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
      gap: 2rem;
    }
  }

  /* @media (min-width: 1000px) {
    .card-items {
      grid-template-columns: repeat(3, 1fr);
    }
  } */
  .item {
    padding: 1rem;
    border-radius: 0.1rem;
    font-family: var(--font-family-inter);
    cursor: pointer;
    color: #fff;
    /* box-shadow:
      rgba(60, 64, 67, 0.3) 0px 1px 2px 0px,
      rgba(60, 64, 67, 0.15) 0px 1px 3px 1px; */
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    transition: box-shadow 0.5s ease-in-out;
  }

  .slug {
    text-align: center;
  }

  .section-item {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-decoration: none;
    color: inherit;
  }

  .section-item:hover {
    text-decoration: underline;
  }

  .slug-item {
    margin: 0;
    text-align: start;
  }

  /* .sub-section {
    font-size: 1rem;
    font-weight: 400;
    margin-left: 1rem;
    text-decoration: none;
    cursor: pointer;
  }

  .sub-section:hover {
    text-decoration: underline;
  } */

  .item:hover {
    box-shadow:
      0 3px 6px rgba(0, 0, 0, 0.16),
      0 3px 6px rgba(0, 0, 0, 0.23);
  }

  .about {
    margin-top: 2rem;
  }

  .about h2,
  h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .about p {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 1rem;
  }

  .bolded {
    font-size: 1.5rem;
    font-weight: 500;
    padding: 1rem;
    text-align: left;
  }

  .tag-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  @media screen and (width <= 800px) {
    .about h2,
    h3 {
      font-size: 1.2rem;
    }
    .about p {
      font-size: 0.8rem;
    }
    /* h4 {
      font-size: 1rem;
    } */

    .bolded {
      font-size: 1rem;
    }
  }

  /* .tagItems {
    margin-bottom: 2rem;
  }

  .tag-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    justify-content: center;
    align-items: center;
  } */

  .tag-image {
    width: 120px;
    /* height: 50px; */
    overflow: hidden;
    border-radius: 0.5rem;
  }

  .tag-image > img {
    width: 100%;
    height: 100%;
    /* padding: 2rem; */
    /* border-radius: 50%;
    background-color: #ddd; */
  }

  /* @media screen and (width >= 1920px) {
    .tag-image {
      width: 100px;
      height: 80px;
    }
  } */

  .hide {
    display: none;
  }

  .inactive {
    cursor: not-allowed;
  }

  /* .flex {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
  } */

  /* I want a grid of 3's */
  .flex {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
    grid-gap: 10px;
    gap: 1rem;
    width: 100%;
  }

  /* I want a grid of 2's */

  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40%, 1fr));
    grid-gap: 10px;
    gap: 1rem;
    width: 100%;
  }
  /* 
  @media screen and (width >= 800px) {
    /* if flex has 2 children
    .flex:nth-child(2) > a:nth-child(1) {
      margin-left: auto;
    }
    .flex:nth-child(2) > a:nth-child(2) {
      margin-right: auto;
      margin-left: auto;
    } */

  /* if we have 4 children */
  /* arrange children in grid of twos */
  /* .flex:nth-child(4) > a:nth-child(1),
    .flex:nth-child(4) > a:nth-child(2) {
      width: 50%;
    } */
  /* } */
  .choir {
    margin-bottom: 2rem;
  }

  @media screen and (width <= 500px) {
    .flex {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .choir p {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 1rem;
  }
  .choir ul {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 1rem;
  }

  .choir ul li {
    margin-left: 2rem;
    list-style-type: upper-roman;
  }

  .late {
    font-size: 0.7rem;
  }

  .benefits-table {
    border-collapse: collapse;
  }
  .benefits-table tr {
    border-bottom: 1px solid #ddd;
    display: flex;
  }

  @media screen and (width <= 500px) {
    .benefits-table tr {
      flex-wrap: wrap;
    }
  }

  td {
    border: none;
    display: flex;
    align-items: center;
  }

  .bolded {
    min-width: 30%;
    text-wrap: wrap;
  }

  .soon {
    padding-top: 3rem;
    margin-top: 5rem;
    border-top: solid 1px #ddd;
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .soon p {
    font-size: 1.1rem;
    font-weight: 600;
    white-space: nowrap;
  }

  .coming-soon {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    width: 90%;
  }

  @media screen and (width <= 800px) {
    .soon p {
      font-size: 0.8rem;
    }

    .coming-soon,
    .soon {
      flex-direction: column;
      align-items: start;
    }

    .soon-item {
      width: 100%;
    }
  }

  .soon-item {
    flex: 1;
    padding: 0.5rem;
    border-radius: 0.1rem;
    font-family: var(--font-family-inter);
    cursor: pointer;
    color: #fff;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    transition: box-shadow 0.5s ease-in-out;
    background-color: #141c4c;
    opacity: 0.5;
  }

  .wonder {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
  }

  .slack {
    background-color: #141c4c;
    color: #fff;
    text-align: center;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    margin: auto 0;
    padding: 0.8rem;
  }
</style>
