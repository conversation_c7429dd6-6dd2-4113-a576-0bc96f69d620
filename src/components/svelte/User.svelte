<script>
  import { onMount } from 'svelte';

  let showDropdown = false;
  export let initials = 'MN';

</script>

<div
  class="avatar"
  on:click={() => showDrawer()}
  on:keydown={(e) => {
    if (e.key === 'Enter') {
      showDrawer();
    }
  }}
  role="button"
  tabindex="0"
>
  {#if initials}
    {initials}
  {/if}
</div>

<style>
  .avatar {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ccc;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
  }

  .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 150px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px;
    display: none;
    /* display: ${showDropdown ? 'block' : 'none'}; */
  }

  .dropdown-item {
    padding: 4px 8px;
    cursor: pointer;
  }

  .hidden {
    display: none;
  }
</style>
