<script lang="ts">
  import * as jose from 'jose';

  import { onMount } from 'svelte';
  import { showSearch } from './stores';
  import type { Post } from '../../lib/interfaces';

  export let allPosts: Post[];

  $: feedItems = [];
  $: fetched = false;

  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };
  const secret = import.meta.env.PUBLIC_SECRET_KEY;

  $: localUserSeries = '';
  $: localType = '';

  let mounted = false;

  const getUser = async () => {
    const token = retreiveToken();
    if (!token) {
      return {
        userSeries: '',
        usertype: '',
      };
    }

    const _secret = new TextEncoder().encode(secret);
    const { payload } = await jose.jwtVerify<{
      userId: string;
      sessionId: string;
      isAdmin: boolean;
      usertype: string;
      userSeries: string;
      exp: number;
    }>(token, _secret);

    const { userSeries, usertype } = payload;

    return { userSeries, usertype };
  };

  const handleClickOutside = () => {
    showSearch.set(false);
  };

  function clickOutside(node) {
    const handleClick = (event) => {
      if (node && !node.contains(event.target) && !event.defaultPrevented) {
        node.dispatchEvent(new CustomEvent('click_outside', node));
      }
    };

    document.addEventListener('click', handleClick, true);

    return {
      destroy() {
        document.removeEventListener('click', handleClick, true);
      },
    };
  }

  let searchQuery = '';
  let filteredItems = [];

  function filterItems() {
    filteredItems = feedItems.filter((item) =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }

  async function fetchPosts() {
    const allPostsType = allPosts.filter((_post) => _post.type === localType);

    const allPostAllowed = allPostsType.filter((_post) => {
      if (!_post?.For) return true;

      if (_post?.For?.length === 0) {
        return true;
      }

      const _fors = _post?.For?.map((s) => s.name.toLowerCase());

      const index = _fors.findIndex((f) => f === localUserSeries);

      if (index === -1) {
        return false;
      }

      if (index && index >= 0 && index !== -1) {
        return true;
      }

      return false;
    });

    const sortedPost = allPostAllowed.sort((a, b) => {
      if (a.Rank < b.Rank) {
        return -1;
      } else if (a.Rank === b.Rank) {
        return 0;
      }
      return 1;
    });

    const posts = sortedPost.map((post) => {
      const { Title, PageId } = post;
      return {
        title: Title,
        link: `https://resources.cre.vc/resource/${PageId}?resource_name=${encodeURIComponent(Title)}`,
      };
    });
    return posts;
  }

  onMount(async () => {
    const { userSeries, usertype } = await getUser();
    localUserSeries = userSeries;
    localType = usertype;
    mounted = true;
    feedItems = await fetchPosts();
    fetched = true;
  });
</script>

{#if mounted}
  {#if $showSearch}
    <div class="search-modal">
      <div
        class="search-modal-content"
        use:clickOutside
        on:click_outside={handleClickOutside}
      >
        <input
          type="text"
          class="search-input"
          placeholder="Search..."
          bind:value={searchQuery}
          on:input={filterItems}
          id="search-input"
        />

        {#if fetched}
          {#if filteredItems.length > 0}
            <ul class="feed-list">
              {#each filteredItems as item}
                <li class="feed-item">
                  <a href={item.link}>{item.title}</a>
                  <!-- <p>{item.description}</p>
                <p>{item.pubDate}</p> -->
                </li>
              {/each}
            </ul>
          {:else if feedItems.length > 0}
            <ul class="feed-list">
              {#each feedItems as item}
                <li class="feed-item">
                  <a href={item.link}>{item.title}</a>
                  <!-- <p>{item.description}</p>
                <p>{item.pubDate}</p> -->
                </li>
              {/each}
            </ul>
          {/if}
        {:else if searchQuery !== '' && filteredItems.length === 0}
          <p>No results</p>
        {/if}
      </div>
    </div>
  {/if}
{/if}

<style>
  .search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .search-modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
  }

  .search-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 10px;
  }

  .feed-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    max-height: 80vh;
    overflow-y: auto;
  }

  .feed-item {
    padding: 10px;
    border-bottom: 1px solid #ccc;
  }
</style>
