<script>
  import { Subscribe, Render } from 'svelte-headless-table';
  // import CaretIcon from '../icons/app/CaretIcon.svelte';

  export let tableObject;

  const { headerRows, rows, tableAttrs, tableBodyAttrs, pluginStates } =
    tableObject.table.createViewModel(tableObject.columns);
</script>

<div class="table-container">
  <table {...$tableAttrs}>
    <thead>
      {#each $headerRows as headerRow (headerRow.id)}
        <Subscribe rowAttrs={headerRow.attrs()} let:rowAttrs>
          <tr {...rowAttrs}>
            {#each headerRow.cells as cell (cell.id)}
              <Subscribe
                attrs={cell.attrs()}
                let:attrs
                props={cell.props()}
                let:props
              >
                <th {...attrs} on:click={props.sort.toggle}>
                  <Render of={cell.render()} />
                  <!-- {#if props.sort.order === 'asc'}
                    <CaretIcon open={false} />
                  {:else if props.sort.order === 'desc'}
                    <CaretIcon open={true} />
                  {/if} -->
                </th>
              </Subscribe>
            {/each}
          </tr>
        </Subscribe>
      {/each}
    </thead>
    <tbody {...$tableBodyAttrs}>
      {#each $rows as row (row.id)}
        <Subscribe rowAttrs={row.attrs()} let:rowAttrs>
          <tr {...rowAttrs}>
            {#each row.cells as cell (cell.id)}
              <Subscribe attrs={cell.attrs()} let:attrs>
                <td {...attrs}>
                  <Render of={cell.render()} />
                </td>
              </Subscribe>
            {/each}
          </tr>
        </Subscribe>
      {/each}
    </tbody>
  </table>
</div>

<style>
  table {
    margin: 50px auto;
    max-width: 800px;
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
  }
  thead {
    width: 100%;
    flex: 1;
  }

  thead tr {
    background: #222;
    color: #fff;
    text-align: left;
    display: flex;
    flex: 1;
  }

  thead th {
    flex: 1;
    padding: 15px 15px;
    text-transform: capitalize;
    font-size: 0.9em;
    font-weight: 700;
    color: #f1f1f1;
    background: #222;
  }

  tbody {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }

  tbody tr {
    background: #fff;
    transition: ease-in-out 0.3s;
    display: flex;
    flex: 1;
  }

  tbody tr:nth-child(even) {
    background: #fafafa;
  }

  tbody tr:hover {
    background: #222;
    color: #f1f1f1;
  }

  tbody td {
    padding: 15px 15px;
    font-size: 0.9em;
    font-weight: 600;
    cursor: pointer;
    flex: 1;
  }

  @media screen and (max-width: 600px) {
    table {
      box-shadow: none;
      margin: 0;
    }

    table,
    thead,
    tbody,
    thead th,
    tbody td,
    tbody tr {
      display: block;
    }

    thead tr {
      display: none;
    }

    tbody tr {
      margin: 40px 10px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    tbody td {
      position: relative;
      border: none;
      border-bottom: 1px solid #eee;
      padding-left: 50%;
    }

    tbody td:before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      height: 100%;
      width: 40%;
      background: #222;
    }

    tbody td:after {
      position: absolute;
      color: #fff;
      text-transform: capitalize;
      font-size: 0.9em;
      top: 15px;
      left: 15px;
      height: 100%;
      width: 40%;
      white-space: nowrap;
    }

    tbody td:nth-of-type(1):after {
      content: 'Name';
    }
    tbody td:nth-of-type(2):after {
      content: 'Email';
    }
    tbody td:nth-of-type(3):after {
      content: 'VC Type';
    }

    tbody td:nth-of-type(4):after {
      content: 'Seed Type';
    }

    tbody td:nth-of-type(4):after {
      content: 'Actions';
    }

    tbody td:last-child {
      border-bottom: none;
    }

    tbody tr:hover {
      background: #fff;
      color: #222;
    }
  }
</style>
