<script lang="ts">
  import { slide } from 'svelte/transition';
  import Button from '../forms/Button.svelte';
  import TextInput from '../forms/TextInput.svelte';
  import { z } from 'astro:content';
  import * as jose from 'jose';
  import <PERSON><PERSON>tch<PERSON> from 'svelte-hcaptcha';

  const PUBLIC_HCAPTCHA_SITE_KEY = import.meta.env.PUBLIC_HCAPTCHA_SITE_KEY;

  const emailSchema = z.object({
    email: z.string().email(),
  });
  let form = {
    email: '',
    otp: '',
  };

  let errors = {
    email: '',
    otp: '',
  };
  let fetching = false;
  let otpScreen = false;
  const handleRequestOTP = async (e) => {
    if (fetching) return;
    const email = form.email;
    const result = emailSchema.safeParse({ email });

    if (!result.success) {
      errors = result.error.formErrors.fieldErrors;
      return;
    }
    fetching = true;
    await fetch('/api/authentication', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, passToken }),
    }).finally(() => {
      fetching = false;
      otpScreen = true;
      passToken = undefined;
      passed = false;
    });
  };

  const emailOtpSchema = z.object({
    email: z.string().email(),
    otp: z.string().length(6, 'OTP must be 6 digits'),
  });
  const secret = import.meta.env.PUBLIC_SECRET_KEY;

  const createAndAddTokenToLocalStorage = async (user, session) => {
    if (!user || !session) return;
    const secretKeyUint8Array = new TextEncoder().encode(secret);

    const alg = 'HS256';

    const token = await new jose.SignJWT({
      userId: user._id,
      sessionId: session._id,
      isAdmin: user.accessLevel === 'admin',
      usertype: user.vcType,
      userSeries: user.seedType,
      exp: Date.now() / 1000 + 60 * 60 * 24 * 1, // 1 day
    })
      .setProtectedHeader({ alg })
      .setIssuedAt()
      .setExpirationTime('24h')
      .sign(secretKeyUint8Array);

    localStorage.setItem('token', token);
  };

  const handleVerifyOTP = async (e) => {
    if (fetching) return;
    const result = emailOtpSchema.safeParse(form);
    if (!result.success) {
      errors = result.error.formErrors.fieldErrors;

      return;
    }
    fetching = true;
    await fetch('/api/authentication', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(form),
    })
      .then(async (res) => {
        if (res.status === 200) {
          const data = await res.json();
          await createAndAddTokenToLocalStorage(data?.user, data?.session);
          window.location.href = '/';
        } else {
          errors = {
            otp: 'Invalid OTP',
          };
        }
      })
      .finally(() => {
        fetching = false;
      });
  };

  let passed = false;
  let passToken = undefined;
</script>

<div class="auth">
  {#if !otpScreen}
    <TextInput
      placeholder="Email"
      required={true}
      autocomplete="email"
      bind:value={form.email}
      isInvalid={errors?.email ? true : false}
      disabled={fetching}
      on:keydown={(e) => {
        if (e.key === 'Enter') {
          handleRequestOTP();
        }
      }}
    />
    <HCaptcha
      sitekey={PUBLIC_HCAPTCHA_SITE_KEY}
      on:success={(e) => {
        passToken = e.detail.token;
        passed = true;
      }}
      on:error={() => {
        passToken = undefined;
        passed = false;
      }}
    />
    {#if passed}
      <Button
        text={fetching ? 'Sending' : 'Request Access'}
        on:click={handleRequestOTP}
      />
    {/if}
  {:else}
    <div>
      <p>
        An OTP has been sent to your email. Please enter it below to continue.
      </p>
    </div>
    <div class="full" transition:slide>
      <TextInput
        placeholder="OTP"
        required={true}
        autocomplete="one-time-code"
        bind:value={form.otp}
        isInvalid={errors?.otp ? true : false}
        disabled={fetching}
        description={errors?.otp
          ? errors.otp
          : 'Please enter the 6 digit OTP sent to your email.'}
      />
    </div>
    <Button
      text={fetching ? 'Authenticating' : 'Enter OTP'}
      on:click={handleVerifyOTP}
    />
  {/if}
</div>

<style>
  .auth {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    margin-top: 1rem;
    padding: 2rem;
  }

  :global(.auth .button) {
    text-align: center;
  }

  .full {
    width: 100%;
  }
</style>
