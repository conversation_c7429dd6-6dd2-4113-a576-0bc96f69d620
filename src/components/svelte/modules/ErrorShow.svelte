<script lang="ts">
  import { onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import { SvelteToast, toast } from '@zerodevx/svelte-toast';

  const globalErrorObject = writable({
    errorNotification: false,
    errorMessage: '',
  });

  onMount(() => {
    window.onunhandledrejection = function (e) {
      $globalErrorObject.errorMessage = e.reason.message;
      $globalErrorObject.errorNotification = true;
    };
  });

  $: if ($globalErrorObject.errorNotification) {
    toast.push($globalErrorObject.errorMessage, {
      duration: 5000,
      dismissable: true,
    });
    $globalErrorObject.errorNotification = false;
  }
</script>

<SvelteToast />
