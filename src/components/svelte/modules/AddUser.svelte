<script lang="ts">
  import { z } from 'astro:content';
  import type { IUserCreate } from '../../../lib/schemas';
  import TextInput from '../forms/TextInput.svelte';
  import Select from '../forms/Select.svelte';
  import Button from '../forms/Button.svelte';
  import { toast } from '@zerodevx/svelte-toast';
  import { createEventDispatcher, onMount } from 'svelte';
  import * as jose from 'jose';
  $: token = '';
  $: mounted = false;
  const dispatch = createEventDispatcher();
  const secret = import.meta.env.PUBLIC_SECRET_KEY;
  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };
  onMount(async () => {
    token = retreiveToken();

    const _secret = new TextEncoder().encode(secret);
    const { payload } = await jose.jwtVerify<{
      userId: string;
      sessionId: string;
      isAdmin: boolean;
      usertype: string;
      userSeries: string;
      exp: number;
    }>(token, _secret);

    const { sessionId } = payload;
    token = sessionId;
    mounted = true;
  });

  type EditUser = {
    _id?: string;
  } & IUserCreate;

  export let user: EditUser = {
    _id: '',
    fullname: '',
    email: '',
    vcType: 'lp',
    seedType: 'pre-seed',
    accessLevel: 'user',
    company: '',
    role: '',
  };

  const vcType = [
    { name: 'Limited Partner', value: 'lp' },
    { name: 'Founder', value: 'founder' },
  ];

  const seedType = [
    { name: 'Pre Seed', value: 'pre-seed' },
    { name: 'Seed', value: 'seed' },
    { name: 'Series A', value: 'series a' },
    { name: 'Series B', value: 'series b' },
    { name: 'Series C', value: 'series c' },
    { name: 'Series D', value: 'series d' },
  ];

  const accessLevel = [
    { name: 'Moderator', value: 'moderator' },
    { name: 'User', value: 'user' },
    { name: 'Admin', value: 'admin' },
  ];

  let fetching = false;

  $: errors = {
    fullname: '',
    email: '',
    vcType: '',
    seedType: '',
    accessLevel: '',
    company: '',
    role: '',
  };

  const zodSchema = z.object({
    fullname: z.string().min(1, { message: 'Full name is required' }),
    email: z.string().email({ message: 'Invalid email' }),
    vcType: z.enum(['lp', 'founder']),
    seedType: z
      .enum([
        'pre-seed',
        'seed',
        'series a',
        'series b',
        'series c',
        'series d',
      ])
      .optional(),
    accessLevel: z.enum(['moderator', 'user', 'admin']).optional(),
    _id: z.string().optional(),
    company: z.string().optional(),
    role: z.string().optional(),
  });

  const deleteUser = async (e: Event) => {
    if (fetching) return;
    e.preventDefault();
    fetching = true;
    return fetch(`/api/users?token=${token}`, {
      method: 'DELETE',
      body: JSON.stringify({ _id: user._id }),
    })
      .then((res) => res.json())
      .then((data) => data.users)
      .then(() => window.location.reload());
  };

  const handleSubmit = async (e: Event) => {
    if (fetching) return;
    e.preventDefault();
    fetching = true;
    try {
      const validated = zodSchema.parse(user);

      const res = await fetch(`/api/users?token=${token}`, {
        method: 'POST',
        body: JSON.stringify(validated),
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (res.ok) {
        const data = await res.json();
        toast.push(data?.message);
        dispatch('close');
      } else {
        const data = await res.json();
        toast.push(data?.message);
      }
    } catch (err) {
      if (err instanceof z.ZodError) {
        err.errors.forEach((error) => {
          const path = error.path.join('.');
          errors[path] = error.message;
        });
      } else {
        throw err;
      }
    } finally {
      fetching = false;
    }
  };
</script>

<div class="form">
  <div class="form-item">
    <label for="fullname">Full Name</label>
    <TextInput
      type="text"
      id="fullname"
      name="fullname"
      bind:value={user.fullname}
      isInvalid={!!errors.fullname}
      description={errors.fullname}
    />
  </div>
  <div class="form-item">
    <label for="email">Email</label>
    <TextInput
      type="text"
      id="email"
      name="email"
      bind:value={user.email}
      isInvalid={!!errors.email}
      description={errors.email}
    />
  </div>
  <div class="form-item">
    <label for="company">Company</label>
    <TextInput
      type="text"
      id="company"
      name="company"
      bind:value={user.company}
      isInvalid={!!errors.company}
      description={errors.company}
    />
  </div>
  <div class="form-item">
    <label for="role">Role</label>
    <TextInput
      type="text"
      id="role"
      name="role"
      bind:value={user.role}
      isInvalid={!!errors.role}
      description={errors.role}
    />
  </div>

  <div class="form-item">
    <label for="vcType">VC Type</label>
    <Select
      id="vcType"
      options={vcType}
      bind:value={user.vcType}
      isInvalid={!!errors.vcType}
      description={errors.vcType}
    />
  </div>

  {#if user.vcType !== 'lp'}
    <div class="form-item">
      <label for="seedType">Category</label>
      <Select
        id="seedType"
        options={seedType}
        bind:value={user.seedType}
        isInvalid={!!errors.seedType}
        description={errors.seedType}
      />
    </div>
  {/if}

  <div class="form-item">
    <label for="accessLevel">Access Level</label>
    <Select
      id="accessLevel"
      options={accessLevel}
      bind:value={user.accessLevel}
      isInvalid={!!errors.accessLevel}
      description={errors.accessLevel}
    />
  </div>
  <div class="actions">
    <Button
      on:click={handleSubmit}
      text={user._id ? 'Update User' : 'Add User'}
      disabled={fetching}
    />
    {#if user._id}
      <Button
        isDanger={true}
        text="Delete User"
        disabled={fetching}
        on:click={deleteUser}
      />
    {/if}
  </div>
  {#if fetching}
    <div class="loading">
      <p>Fetching...</p>
    </div>
  {/if}
</div>

<style>
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .actions {
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }

  .loading {
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }

  .loading p {
    margin: 0;
  }
</style>
