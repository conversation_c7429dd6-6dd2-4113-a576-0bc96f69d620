<script lang="ts">
  import { onMount } from 'svelte';
  import { createTable, createRender } from 'svelte-headless-table';
  import { addSortBy } from 'svelte-headless-table/plugins';
  import { writable } from 'svelte/store';
  import Table from '../UserTable.svelte';
  import Button from '../forms/Button.svelte';
  import AddUser from './AddUser.svelte';
  import * as jose from 'jose';
  import { toast } from '@zerodevx/svelte-toast';

  $: token = '';
  $: mounted = false;
  const secret = import.meta.env.PUBLIC_SECRET_KEY;

  onMount(async () => {
    token = retreiveToken();

    const _secret = new TextEncoder().encode(secret);
    const { payload } = await jose.jwtVerify<{
      userId: string;
      sessionId: string;
      isAdmin: boolean;
      usertype: string;
      userSeries: string;
      exp: number;
    }>(token, _secret);

    const { sessionId } = payload;
    token = sessionId;
    mounted = true;
  });

  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };

  const fetchUsers = async () => {
    return fetch(`/api/users?token=${token}`)
      .then((res) => {
        if (res.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/';
        }
        return res.json();
      })
      .then((data) => data.users);
  };
  const formatDate = (date: string) => {
    const d = new Date(date);
    return `${d.getMonth() + 1}/${d.getDate()}/${d.getFullYear()}`;
  };

  const usersDataTableStore = writable([]);

  const createUserTableObject = (data) => {
    usersDataTableStore.set(data);

    const table = createTable(usersDataTableStore, {
      sort: addSortBy(),
    });

    const columns = table.createColumns([
      table.column({
        header: 'Name',
        accessor: 'fullname',
      }),
      table.column({
        header: 'Email',
        accessor: 'email',
      }),
      table.column({
        header: 'Company',
        accessor: 'company',
        cell: (cell: any) => {
          const _ = cell.data;
          const originalRow = cell.row.original;
          return originalRow?.company ?? 'nil';
        },
      }),

      table.column({
        header: 'VC Type',
        accessor: 'vcType',
        cell: (cell: any) => {
          const _ = cell.data;
          const originalRow = cell.row.original;
          if (originalRow?.vcType === 'founder') {
            return 'Founder';
          }
          if (originalRow?.vcType === 'lp') {
            return 'LP';
          }
          return originalRow?.vcType ?? 'nil';
        },
      }),
      // table.column({
      //   header: 'Seed Type',
      //   accessor: 'seedType',
      //   cell: (cell: any) => {
      //     const _ = cell.data;
      //     const originalRow = cell.row.original;
      //     // if vcType is === 'founder' then seed type is NA
      //     if (originalRow?.vcType === 'lp') {
      //       return 'N/A';
      //     }
      //     if (originalRow?.seedType === 'pre-seed') {
      //       return 'Pre-Seed';
      //     }
      //     if (originalRow?.seedType === 'seed') {
      //       return 'Seed';
      //     }
      //     if (originalRow?.seedType === 'series a') {
      //       return 'Series A';
      //     }
      //     if (originalRow?.seedType === 'series b') {
      //       return 'Series B';
      //     }
      //     if (originalRow?.seedType === 'series c') {
      //       return 'Series C';
      //     }
      //     if (originalRow?.seedType === 'series d') {
      //       return 'Series D';
      //     }

      //     return originalRow?.seedType ?? 'nil';
      //   },
      // }),
      table.column({
        header: 'Actions',
        accessor: '',
        cell: (cell: any) => {
          const _ = cell.data;
          const originalRow = cell.row.original;
          return createRender(Button, {
            text: 'Manage',
          }).on('click', () => {
            handleManage(originalRow);
          });
        },
      }),
    ]);
    return { table, columns };
  };

  $: showAddUserModal = false;
  $: showDeleteUserModal = false;

  let wipUser = {};

  const handleManage = (user) => {
    wipUser = user;
    showAddUserModal = true;
  };
  let deploying = false;
  const handleRedeploy = async () => {
    if (deploying) {
      return;
    }
    deploying = true;
    const response = await fetch(`/api/redeploy?token=${token}`, {
      method: 'POST',
    });
    if (response.status === 200) {
      throw new Error('Redeploy in progress');
      return;
    }
    throw new Error('Something went wrong');
  };
</script>

<div class="title">
  <h2>User Mangement</h2>
  <div class="title-button">
    <Button text="Redeploy" on:click={handleRedeploy} />
    <Button
      text="Add User"
      on:click={() => {
        showAddUserModal = true;
      }}
    />
  </div>
</div>

{#if mounted}
  {#await fetchUsers()}
    <div class="centered">
      <p>loading...</p>
    </div>
  {:then users}
    <div class="umt">
      <Table tableObject={createUserTableObject(users)} />
    </div>
  {:catch error}
    <div class="centered">
      <p>error: {error.message}</p>
    </div>
  {/await}
{/if}

{#if showAddUserModal || showDeleteUserModal}
  <div class="ovh">
    <div class="modal">
      <div class="modal-header">
        <h3>{showAddUserModal ? 'Add User' : 'Delete User'}</h3>
        <button
          class="close-button"
          on:click={() => {
            showAddUserModal = false;
            showDeleteUserModal = false;
          }}
        >
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <AddUser
          user={wipUser}
          on:close={() => {
            showAddUserModal = false;
            showDeleteUserModal = false;
            wipUser = {};
            window.location.reload();
          }}
        />
      </div>
    </div>
  </div>
{/if}

<style>
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
  }
  .title-button {
    /* max-width: fit-content; */
    display: flex;
    gap: 1rem;
  }

  .centered {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ovh {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }

  .modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    width: 80%;
    min-height: 50%;
    z-index: 1000;
    border-radius: 5px;
    padding: 1rem;
  }

  @media (min-width: 768px) {
    .modal {
      width: 50%;
    }
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .modal-header h3 {
    margin: 0;
  }

  .close-button {
    border: none;
    background: none;
    font-size: 1.5rem;
    cursor: pointer;
  }

  .modal-body {
    height: 100%;
  }
</style>
