<script lang="ts">
  import { slide } from 'svelte/transition';
  import TableOfContents from '../../TableOfContents.svelte';

  import { drawerOpen, userNanoStore } from '../../stores';

  import * as jose from 'jose';
  import { onMount } from 'svelte';
  import SearchBox from '../../SearchBox.svelte';

  export let founderCategories;
  export let lpCategories;
  export let allPosts;
  export let menu = true;

  const handleLogOut = () => {
    localStorage.removeItem('token');
    window.location.href = '/';
  };
  const handleClickOutside = () => {
    drawerOpen.set(false);
  };

  function clickOutside(node) {
    const handleClick = (event) => {
      if (node && !node.contains(event.target) && !event.defaultPrevented) {
        node.dispatchEvent(new CustomEvent('click_outside', node));
      }
    };

    document.addEventListener('click', handleClick, true);

    return {
      destroy() {
        document.removeEventListener('click', handleClick, true);
      },
    };
  }

  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };
  const secret = import.meta.env.PUBLIC_SECRET_KEY;

  const isAdmin = async () => {
    const token = retreiveToken();
    if (!token) {
      return false;
    }

    const _secret = new TextEncoder().encode(secret);
    const { payload } = await jose.jwtVerify<{
      userId: string;
      sessionId: string;
      isAdmin: boolean;
      usertype: string;
      userSeries: string;
      exp: number;
    }>(token, _secret);

    const { isAdmin, usertype, userSeries, exp } = payload;
    if (exp < Date.now() / 1000) {
      return false;
    }
    return isAdmin;
  };

  $: isAdministrator = false;
  onMount(async () => {
    isAdministrator = await isAdmin();
  });

  const switchTo = (type: 'lp' | 'founder') => {
    if (!isAdministrator) {
      return;
    }

    if (!$userNanoStore?.userType) {
      return;
    }

    if (type === 'lp' && $userNanoStore?.userType === 'lp') {
      return;
    }

    if (type === 'founder' && $userNanoStore?.userType === 'founder') {
      return;
    }

    if (type === 'lp') {
      userNanoStore.set({
        ...$userNanoStore,
        userType: 'lp',
      });
    } else {
      userNanoStore.set({
        ...$userNanoStore,
        userType: 'founder',
      });
    }
    localStorage.setItem('adminRequiredType', type);
  };
</script>

<div
  use:clickOutside
  on:click_outside={handleClickOutside}
  class="drawer {$drawerOpen ? 'open' : ''}"
>
  <div class="drawer-content">
    <div class="search">
      <SearchBox />
    </div>

    <div class="menu">
      <div class="drawer-item">
        <a target="_blank" href="https://cre.vc">CRE</a>
      </div>
      {#if isAdministrator}
        <div class="drawer-item">
          <a href="/admin">CRE Admin</a>
        </div>
        <div
          tabindex="0"
          role="button"
          class="drawer-item"
          on:keydown={(e) => {
            if (e.key === 'Enter') {
              switchTo('lp');
              drawerOpen.set(false);
            }
          }}
          on:click={() => {
            switchTo('lp');
            drawerOpen.set(false);
          }}
        >
          LP
        </div>
        <div
          tabindex="0"
          role="button"
          class="drawer-item"
          on:keydown={(e) => {
            if (e.key === 'Enter') {
              switchTo('founder');
              drawerOpen.set(false);
            }
          }}
          on:click={() => {
            switchTo('founder');
            drawerOpen.set(false);
          }}
        >
          Founder
        </div>
      {/if}
      <button class="logout" on:click={handleLogOut}>
        <div class="drawer-item">Logout</div>
      </button>
    </div>
    <div class="tocc">
      <TableOfContents {lpCategories} {founderCategories} {allPosts} {menu} />
    </div>
  </div>
  <div class="placeholder"></div>
</div>

<style>
  a {
    color: inherit;
    text-decoration: none;
  }

  a:visited {
    color: inherit;
    text-decoration: none;
  }
  .drawer {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    width: 85%;
    max-width: 400px;
    height: 100%;
    background-color: #fff;

    z-index: 1000;
    border-left: solid 1px #eaeaea;
  }

  .drawer.open {
    transform: translateX(0);
    display: block;
  }

  .drawer-content {
    padding: 1rem;
  }

  .menu {
    display: grid;
    grid-template-columns: repeat(2, minmax(100px, 1fr));
    gap: 1rem;
  }

  .drawer-item {
    padding: 0.5rem;
    border-radius: 8px;
    margin-bottom: 0.3rem;
    cursor: pointer;
    font-weight: 400;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    background-color: #1c244c;
    color: #fff;
    max-width: 100px;

    text-align: center;
  }

  .tocc {
    display: block;
    margin-top: 0.5rem;
    height: clamp(40vh, 60vh, 70vh);
    overflow-y: auto;
  }

  @media screen and (min-width: 901px) {
    .tocc {
      display: none;
    }
    .drawer {
      max-width: 200px;
    }
    .menu {
      display: flex;
      flex-direction: column;
    }

    .drawer-item {
      width: 100%;
      max-width: none;
    }
  }
  .search {
    margin: 1rem 0 2rem 0;
  }

  @media screen and (min-width: 901px) {
    .search {
      display: none;
    }
  }

  .placeholder {
    height: 1rem;
  }
</style>
