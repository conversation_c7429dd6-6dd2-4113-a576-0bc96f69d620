<script lang="ts">
  import { onMount } from 'svelte';
  import * as jose from 'jose';
  import { isAllowed, userNanoStore } from './stores';
  import { fly } from 'svelte/transition';
  import { cubicOut } from 'svelte/easing';
  import type { Post } from '../../lib/interfaces';
  export let adminOnly: boolean = false;
  export let post: Post | undefined | null = undefined;
  // let vcType: 'lp' | 'founder' | undefined;
  type VcType = 'lp' | 'founder';

  type Series =
    | 'pre-seed'
    | 'seed'
    | 'series a'
    | 'series b'
    | 'series c'
    | 'series d';

  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };

  const removeToken = () => {
    localStorage.removeItem('token');
  };

  const storeUserTypeAndSeries = (userType: VcType, userSeries: Series) => {
    userNanoStore.set({ userType, userSeries });
  };

  const secret = import.meta.env.PUBLIC_SECRET_KEY;

  const checkUserSeries = (userSeries: string) => {
    if (!post) {
      return true;
    }
    const { For } = post;
    if (!For) {
      return true;
    }
    if (For.length === 0) {
      return true;
    }
    const names = For.map((f) => f.name);

    return names.includes(userSeries);
  };

  const checkPermissions = async () => {
    $isAllowed = 'loading';
    try {
      const token = retreiveToken();
      if (!token) {
        return false;
      }

      const _secret = new TextEncoder().encode(secret);
      const { payload } = await jose
        .jwtVerify<{
          userId: string;
          sessionId: string;
          isAdmin: boolean;
          usertype: string;
          userSeries: string;
          exp: number;
        }>(token, _secret)
        .catch((error) => {
          removeToken();
          return { payload: null };
        });

      if (!payload) {
        return false;
      }

      const { isAdmin, usertype, userSeries, exp } = payload;

      let _localType;

      const localStorageAdminRequiredType =
        localStorage.getItem('adminRequiredType');
      if (localStorageAdminRequiredType && isAdmin) {
        // @ts-expect-error
        storeUserTypeAndSeries(localStorageAdminRequiredType, userSeries);
        _localType = localStorageAdminRequiredType;
      } else {
        // @ts-expect-error
        storeUserTypeAndSeries(usertype, userSeries);
        _localType = usertype;
      }

      const isTokenExpired = exp < Date.now() / 1000;
      if (isTokenExpired) {
        removeToken();
        return false;
      }

      if (adminOnly && !isAdmin) {
        return false;
      }

      const postType = post?.type;

      if (!postType) {
        // A post should typically have a type from the database, however, if it doesn't, we'll allow it
        return true;
      }

      if (postType !== _localType && !isAdmin) {
        return false;
      }

      if (postType === 'lp') {
        return true;
      }

      if (!userSeries && !isAdmin) {
        return false;
      }

      if (!isAdmin) {
        return checkUserSeries(userSeries);
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  onMount(async () => {
    try {
      $isAllowed = await checkPermissions();
    } catch (error) {
      $isAllowed = false;
    }
  });
</script>

{#if $isAllowed === true}
  <slot name="allowed" />
{:else if $isAllowed === false}
  <slot name="not-allowed" />
{:else}
  <slot name="loading">
    <div
      class="loading-container"
      transition:fly={{
        delay: 400,
        duration: 500,
        easing: cubicOut,
      }}
    >
      <div class="loading"></div>
    </div>
  </slot>
{/if}

<style>
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  .loading {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    border-radius: 50%;
    width: 90px;
    height: 90px;
    animation: spin 2s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
