<script lang="ts">
  import { onMount } from 'svelte';
  import { fly } from 'svelte/transition';

  let loading = true;

  $: news = [];

  const storeLocalNewsCache = (news: []) => {
    const currentTimestamp = new Date().getTime();
    const expiryTimestamp = currentTimestamp + 1000 * 60 * 60 * 24 * 1; // 1 day
    localStorage.setItem(
      'newsCache',
      JSON.stringify({
        news,
        expiryTimestamp,
      })
    );
  };

  const getLocalNewsCache = () => {
    const newsCache = localStorage.getItem('newsCache');
    if (!newsCache) return null;
    const { news, expiryTimestamp } = JSON.parse(newsCache);
    const currentTimestamp = new Date().getTime();
    if (currentTimestamp > expiryTimestamp) {
      localStorage.removeItem('newsCache');
      return null;
    }
    if (news.length === 0) {
      localStorage.removeItem('newsCache');
      return null;
    }
    return news;
  };

  const fetchNewsApi = async () => {
    const localNewsCache = getLocalNewsCache();
    if (localNewsCache) {
      return localNewsCache;
    }
    const response = await fetch('/api/news');
    const data = await response.json().catch((error) => {
      return [];
    });
    storeLocalNewsCache(data.news);
    return data.news ?? [];
  };

  onMount(async () => {
    loading = true;
    news = await fetchNewsApi();
    loading = false;
  });
</script>

{#if loading}
  <div class="xs-container">
    <div
      class="loading-container"
      transition:fly={{
        delay: 200,
        duration: 900,
      }}
    >
      <div class="loading"></div>
      <p>Fetching latest news</p>
    </div>
  </div>
{:else}
  <div class="container">
    <div class="grid-container">
      {#each news as item}
        <a
          class="news-card"
          target="_blank"
          href={`${
            item.link.startsWith('/') ? 'https://cre.vc' + item.link : item.link
          }`}
        >
          <img src={item.imgSrc} alt={item.title} />
          <p>{item.time}</p>
          <p>{item.title}</p>
        </a>
      {/each}
    </div>
  </div>
{/if}

<style>
  .xs-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 250px;
  }

  .container {
    width: 100%;
    min-width: 200px;
    max-width: 350px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 1rem;
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  @media (max-width: 1200px) {
    .grid-container,
    .container,
    .xs-container {
      display: none;
    }
  }

  .news-card {
    padding: 0.5rem;
  }

  .news-card:hover {
    box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 3px 0px;
  }

  .news-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 0.1rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
  }
  .loading {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  a p {
    margin: 0;
  }
</style>
