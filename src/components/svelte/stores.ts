import { writable } from 'svelte/store'
import { atom } from 'nanostores'

export const userNanoStore = atom<{
  userType: 'founder' | 'lp' | undefined
  userSeries:
    | 'pre-seed'
    | 'seed'
    | 'series a'
    | 'series b'
    | 'series c'
    | 'series d'
    | undefined
} | null>(null)

export const isAllowed = writable<'loading' | false | true>('loading')

export const drawerOpen = writable(false)

export const showSearch = writable(false)

export const secretsHREF = writable<string | null>(null)
