<script>
  /** @type {HTMLButtonElement | null} */
  export let ref = null;

  /** @type {string} */
  export let text = '';

  export let fontSizeModifier = 0.75;
  /**
   * @type {boolean|null}
   */
  export let disabled = null;
  /**
   * @typedef {'submit' | 'button' | 'reset'} Type
   */
  /**
   * @type {Type}
   */
  export let type = 'button';
  export let isDanger = false;
</script>

<!-- 
    @component This component is used to create a button.
    @slot `left` - Insert content that resides left of the button.
    @slot default - Insert content that resides inside the button. If no content is provided, the text prop will be used.
    @slot right - Insert content that resides right of the button.
 -->

<div
  on:click={() => {
    if (ref) {
      ref.click();
    }
  }}
  on:keydown={(e) => {
    if (e.key === 'Enter' && ref) {
      ref.click();
    }
  }}
  class="button"
  class:danger={isDanger}
>
  {#if $$slots.left}
    <div class="left">
      <slot name="left" />
    </div>
  {/if}
  <button on:click bind:this={ref} {disabled} {type} class:disabled>
    {#if $$slots.default}
      <slot />
    {:else}
      {text}
    {/if}
  </button>
  {#if $$slots.right}
    <div class="right">
      <slot name="right" />
    </div>
  {/if}
</div>

<style>
  .button {
    appearance: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: var(--radius);
    color: var(--bg);
    background-color: var(--accents-1);
    font-weight: 600;
    line-height: 1rem;
    text-align: center;
    padding: 0.7rem;
    width: 100%;
  }
  .button:hover {
    cursor: pointer;
    background-color: var(--geist-foreground);
  }
  .button:disabled {
    cursor: not-allowed;
    background-color: var(--accents-2);
  }
  .button:disabled:hover {
    background-color: var(--accents-2);
  }
  .left {
    display: flex;
    align-items: center;
    margin-right: 12px;
  }
  .right {
    display: flex;
    align-items: center;
    margin-left: 12px;
  }
  button {
    width: 100%;
    font-family: inherit;
    font-weight: inherit;
  }

  .danger {
    background-color: var(--error);
    /* color: var(--geist-background); */
  }
</style>
