<script>
  /**
   * @type {HTMLInputElement|null}
   * @default null
   */
  export let ref = null
  /**
   * @type {string}
   */
  export let type = 'text'
  /**
   * @type {string|number}
   * @default ''
   */
  export let value = ''
  /**
   * @type {string}
   * @default ''
   */
  export let id = ''
  /**
   * @type {string}
   * @default ''
   */
  export let name = ''
  /**
   * @type {boolean|null}
   * @default null
   */
  export let disabled = null
  /**
   * @type {boolean|null}
   * @default null
   */
  export let readonly = null
  /**
   * @type {string}
   * @default ''
   */
  export let placeholder = ''
  /**
   * @type {boolean|null}
   * @default null
   */
  export let required = null
  /**
   * @type {boolean|null}
   * @default null
   */
  /**
   * @type {string | undefined}
   * @default ''
   */
  export let autocomplete = undefined

  /**
   * @type {string?}
   */
  export let description = null

  /**
   * @type {boolean}
   */
  export let isInvalid = false
  /**
   * @type {Function}
   * @param {Event} e
   * @returns {void}
   */
  const onInput = (e) => {
    const target = e.target
    value = target?.value
  }
  const removePreviousErrors = () => {
    if (isInvalid) {
      description = ''
    }
    isInvalid = false // reset isInvalid when the user clicks on the form element
  }

  let isShaking = false
  function shake() {
    isShaking = true
    setTimeout(() => {
      isShaking = false
    }, 500)
  }

  // Reactivity
  $: if (isInvalid && !isShaking) {
    shake()
  }
</script>

<div class="text-input">
  <input
    bind:this={ref}
    {id}
    {name}
    {type}
    {value}
    {disabled}
    {readonly}
    {required}
    {placeholder}
    {autocomplete}
    on:click={removePreviousErrors}
    on:focus={removePreviousErrors}
    on:click
    on:change
    on:keydown
    on:input={onInput}
    on:input
    class:invalid={isInvalid}
    class:shaking={isShaking}
  />
  {#if description}
    <p class="description" class:invalid={isInvalid}>{description}</p>
  {/if}
</div>

<style>
  .text-input {
    display: flex;
    margin-top: 0.875rem;
    flex-direction: column;
    width: 100%;
  }

  input {
    padding: 0.5rem;
    font-size: 0.875rem;
    font-weight: 400;
    width: 100%;
    appearance: none;
    background: transparent;
    color: inherit;
    transition: all 0.2s ease 0s;
    border: solid 1px var(--accents-1);
    border-radius: var(--radius);
    text-align: left;
    font-family: inherit;
  }
  input.invalid {
    border: solid 1px var(--error);
  }
  :focus:not(:focus-visible) {
    outline: none;
  }
  :focus-visible {
    outline: solid 1px var(--accents-1);
    color: inherit
  }
  p {
    font-size: var(--s);
    font-weight: 300;
    text-align: left;
    margin-top: 0.5rem;
    padding-left: 0.5rem;
  }
</style>
