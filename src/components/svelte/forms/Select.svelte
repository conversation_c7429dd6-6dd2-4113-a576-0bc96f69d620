<script>
  /**
   * @typedef {Object} SelectOption
   * @property {string} name
   * @property {string} value
   */
  /**
   * @type {SelectOption[]}
   */
  export let options = [];
  export let placeholder = undefined;
  // If there is no placeholder, we set the value to the first option
  export let value = placeholder
    ? undefined
    : options.length
    ? options[0].value
    : '';
  export let isInvalid = false;
  export let description = undefined;
  export let id = '';

  const removePreviousErrors = () => {
    if (isInvalid) {
      description = '';
    }
    isInvalid = false; // reset isInvalid when the user clicks on the form element
  };

  let isShaking = false;
  function shake() {
    isShaking = true;
    setTimeout(() => {
      isShaking = false;
    }, 500);
  }

  // Reactivity
  $: if (isInvalid && !isShaking) {
    shake();
  }
</script>

<select
  {id}
  on:click={removePreviousErrors}
  on:click
  bind:value
  on:change
  on:contextmenu
  on:input
  class:invalid={isInvalid}
  class:shaking={isShaking}
>
  {#if placeholder}
    <option value="" disabled selected hidden>{placeholder}</option>
  {/if}
  {#each options as { name, value }}
    <option {value}>{name}</option>
  {/each}
</select>
{#if description}
  <p class="description">{description}</p>
{/if}

<style>
  select {
    outline: none; /* Remove the default focus outline */
    display: block;
    border: solid 1px var(--accents-1);
    border-radius: var(--radius);
    min-width: 11rem;
    width: 100%;
    padding: 0.5rem 0.9rem;
    background-color: #fff;
    color: inherit;
    cursor: pointer;

    /* arrowIcon
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    background-image: url('/icons/caret-down.svg');
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1rem;
    padding-right: 1rem; */
  }

  select.invalid {
    border: solid 1px var(--error);
  }

  p {
    font-size: var(--s);
    font-weight: 200;
    text-align: left;
    margin-top: 0.5rem;
    padding-left: 0.5rem;
  }
</style>
