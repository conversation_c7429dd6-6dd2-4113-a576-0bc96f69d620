<script lang="ts">
  import User from './User.svelte';
  import Icon from '@iconify/svelte';
  import { drawerOpen } from './stores';
</script>

<!-- <div class="user-av">
  <User />
</div> -->
<div
  on:click={() => {
    drawerOpen.set(true);
  }}
  on:keydown={(e) => {
    if (e.key === 'Enter') {
      drawerOpen.set(true);
    }
  }}
  tabindex="0"
  role="button"
  id="hamburger-menu"
  class="hamburger-menu"
>
  <Icon icon="mdi:menu" />
</div>

<style>
  .hamburger-menu {
    display: block;
    width: 1.5rem;
  }
</style>
