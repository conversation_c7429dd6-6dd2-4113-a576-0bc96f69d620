<script lang="ts">
  import * as jose from 'jose';
  import type { Post } from '../../lib/interfaces';
  import { onMount } from 'svelte';

  export let allPosts: Post[];
  export let post: Post | null = null;

  const retreiveToken = () => {
    const token = localStorage.getItem('token');
    if (token) {
      return token;
    }
    return '';
  };
  const secret = import.meta.env.PUBLIC_SECRET_KEY;

  $: localUserSeries = '';

  let mounted = false;

  onMount(async () => {
    const { userSeries } = await getUser();
    localUserSeries = userSeries;
    mounted = true;
  });

  const getUser = async () => {
    const token = retreiveToken();
    if (!token) {
      return {
        userSeries: '',
        usertype: '',
      };
    }

    const _secret = new TextEncoder().encode(secret);
    const { payload } = await jose.jwtVerify<{
      userId: string;
      sessionId: string;
      isAdmin: boolean;
      usertype: string;
      userSeries: string;
      exp: number;
    }>(token, _secret);

    const { userSeries, usertype } = payload;

    return { userSeries, usertype };
  };

  const allPostsForCategory = allPosts.filter(
    (_post) =>
      _post.Category.id === post?.Category.id && _post.type === post?.type
  );

  const allPostAllowed = allPostsForCategory.filter((_post) => {
    if (!_post?.For) return true;

    if (_post?.For?.length === 0) {
      return true;
    }

    const _fors = _post?.For?.map((s) => s.name.toLowerCase());

    const index = _fors.findIndex((f) => f === localUserSeries);

    if (index === -1) {
      return false;
    }

    if (index && index >= 0 && index !== -1) {
      return true;
    }

    return false;
  });

  const sortedPost = allPostAllowed.sort((a, b) => {
    if (a.Rank < b.Rank) {
      return -1;
    } else if (a.Rank === b.Rank) {
      return 0;
    }
    return 1;
  });

  const generatePrev = () => {
    if (!post) {
      return null;
    }

    const index = sortedPost.findIndex(
      (_post) => _post.PageId === post?.PageId
    );

    if (index === 0) {
      return null;
    }

    return sortedPost[index - 1];
  };

  const generateNext = () => {
    if (!post) {
      return null;
    }

    const index = sortedPost.findIndex(
      (_post) => _post.PageId === post?.PageId
    );

    if (index + 1 === sortedPost.length) {
      return null;
    }

    return sortedPost[index + 1];
  };

  $: prev = null;
  $: next = null;
  onMount(async () => {
    prev = generatePrev();

    next = generateNext();
  });
</script>

{#if mounted}
  <div class="pagination">
    <div class="left">
      {#key prev}
        {#if prev}
          <a
            href={`/resource/${prev?.PageId ?? ''}?resource_name=${encodeURIComponent(prev.Title)}`}
          >
            <span class="arrow">&larr;</span>
            {prev?.Title ?? ''}
          </a>
        {/if}
      {/key}
    </div>
    <div class="right">
      {#if next}
        <a
          href={`/resource/${next?.PageId ?? ''}?resource_name=${encodeURIComponent(prev.Title)}`}
        >
          {next?.Title ?? ''} <span class="arrow">&rarr;</span>
        </a>
      {/if}
    </div>
  </div>
{/if}
