<script lang="ts">
  import { onMount } from 'svelte';
  import type { Post, SelectProperty } from '../../lib/interfaces';
  import { userNanoStore, secretsHREF } from './stores';

  export let founderCategories: SelectProperty[];
  export let lpCategories: SelectProperty[];
  export let allPosts: Post[];
  export let menu = false;

  $: isLp = $userNanoStore?.userType === 'lp';

  const categoriesTag = new Map<string, Set<string>>();

  const buildSubsections = (
    type: 'founder' | 'lp',
    series: string,
    category: string
  ) => {
    const postsForType = allPosts.filter((post) => {
      return post.type === type;
    });

    if (type === 'lp') {
      return postsForType
        .filter((post) => {
          return post.Category.id === category;
        })
        .sort((a, b) => {
          if (a.Rank < b.Rank) {
            return -1;
          } else if (a.Rank === b.Rank) {
            return 0;
          }
          return 1;
        })
        .map((post) => {
          if (post.Tag) {
            if (categoriesTag.has(post.Category.id)) {
              categoriesTag.get(post.Category.id)?.add(post.Tag.name);
            } else {
              categoriesTag.set(post.Category.id, new Set([post.Tag.name]));
            }

            return {
              id: post.Tag.name,
              title: post.Tag.name,
              isTag: true,
            };
          }

          return {
            id: post.PageId,
            title: post.Title,
            isTag: false,
          };
        });
    }

    return postsForType
      .filter((post) => {
        return post.Category.id === category;
      })
      .sort((a, b) => {
        if (a.Rank < b.Rank) {
          return -1;
        } else if (a.Rank === b.Rank) {
          return 0;
        }
        return 1;
      })
      .filter((post) => {
        if (!post?.For) return true;
        if (post?.For?.length === 0) {
          return true;
        }
        const _postSeries = post?.For?.map((s) => s.name.toLowerCase());
        return _postSeries.includes(series);
      })
      .map((post) => {
        // If a post has a tag, then the tag should be shown instead of the post title
        if (post.Tag) {
          if (categoriesTag.has(post.Category.id)) {
            categoriesTag.get(post.Category.id)?.add(post.Tag.name);
          } else {
            categoriesTag.set(post.Category.id, new Set([post.Tag.name]));
          }
          return {
            id: post.Tag.name,
            title: post.Tag.name,
            isTag: true,
          };
        }
        return {
          id: post.PageId,
          title: post.Title,
          isTag: false,
        };
      });
  };

  const buildContent = (isLp: boolean) => {
    let categories = founderCategories;
    if (isLp) {
      categories = lpCategories;
    }
    return categories.map((category) => {
      return {
        type: 'section',
        id: category.id,
        title: category.name,
        subsections:
          category.name?.toLowerCase() === 'benefits and partnerships'
            ? []
            : (buildSubsections(
                isLp ? 'lp' : 'founder',
                $userNanoStore?.userSeries ?? '',
                category.id
              ) ?? []),
      };
    });
  };

  $: contents = buildContent(isLp);
  $: browserPath = '';
  let customary = '0p';
  $: customary = browserPath;

  onMount(() => {
    browserPath = window.location.pathname;
    let el: HTMLElement | null = null;

    if (isSectionPath(browserPath)) {
      el = document.getElementById(`section-${extractId(browserPath)}`);
    } else if (isResourcePath(browserPath)) {
      el = document.getElementById(`subsection-${extractId(browserPath)}`);
    }

    if (el) {
      el.scrollIntoView();
    }
  });

  const isSectionPath = (path: string) => {
    return path.includes('section');
  };

  const isResourcePath = (path: string) => {
    return path.includes('resource');
  };

  const extractId = (path: string): string => {
    const uuidRegex = /\/([^\/]+)\/$/;
    const match = path.match(uuidRegex);

    if (match && match[1]) {
      return match[1];
    } else {
      return '';
    }
  };
</script>

<div class="toc-container" class:menu>
  <ul class="toc">
    {#key customary}
      {#each contents as item}
        <li class="toc-item">
          {#if item.type === 'section'}
            {#if item.title === 'Benefits and Partnerships'}
              <a
                id={`section-${item.id}`}
                class="toc-link section"
                target="_blank"
                href={$secretsHREF}>{item.title}</a
              >
            {:else}
              <a
                id={`section-${item.id}`}
                class="toc-link section"
                class:active={(isSectionPath(browserPath) &&
                  extractId(browserPath) === item.id) ||
                  (isResourcePath(browserPath) &&
                    item.subsections.some(
                      (subsection) => subsection.id === extractId(browserPath)
                    ))}
                href={`/section/${item.id}?section_name=${encodeURIComponent(item.title)}`}
                >{item.title}</a
              >
            {/if}

            <ul class="toc-sublist">
              {#each item.subsections as subsection}
                {@const isTag = subsection.isTag}

                {#if !isTag}
                  <li class="toc-subitem">
                    <a
                      id={`subsection-${subsection.id}`}
                      class="toc-link"
                      class:active={isResourcePath(browserPath) &&
                        extractId(browserPath) === subsection.id}
                      href={`/resource/${subsection.id}?resource_name=${encodeURIComponent(subsection.title)}`}
                      >{subsection.title}</a
                    >
                  </li>
                {/if}
              {/each}

              {#if categoriesTag.has(item.id) && !isLp}
                {#each Array.from(categoriesTag.get(item.id)) as tag}
                  <li class="toc-subitem">
                    <a
                      id={`subsection-${tag}`}
                      class="toc-link"
                      class:active={isResourcePath(browserPath) &&
                        extractId(browserPath) === tag}
                      href={`/section/${item.id}?section_name=${encodeURIComponent(item.title)}`}
                    >
                      {tag}
                    </a>
                  </li>
                {/each}
              {/if}
            </ul>
          {/if}
        </li>
      {/each}
    {/key}
  </ul>

  <a
    class="slack"
    href="https://n4ohhvgsu9o.typeform.com/to/EAtmpH2z"
    target="_blank"
  >
    Leave Feedback
  </a>
</div>

<style>
  .toc-container {
    width: 300px;
    margin: 1rem;
    display: flex;
    flex-direction: column;
    height: 90%;
  }
  @media screen and (width <= 900px) {
    .toc-container {
      display: none;
    }
    .menu {
      display: block;
    }
  }

  .toc {
    margin: 0;
    margin-top: 2rem;
    padding: 0;
    list-style-type: none;
  }

  .toc-item {
    margin-bottom: 0.5rem;
  }

  .toc-item:hover,
  .toc-link:hover {
    color: #141c4c;
    font-weight: 700;
    outline: none;
  }

  .toc-link {
    text-decoration: none;
    font-weight: 500;
  }

  .section {
    font-weight: 700;
  }

  .toc-subitem {
    margin-left: 1rem;
  }

  .active {
    color: #fff;
    font-weight: 700;
    background-color: #141c4c;
    padding: 0.2rem;
    margin: 0.2em 0;
    border-radius: 4px;
  }

  .active:hover {
    color: #fff;
  }

  .slack {
    background-color: #141c4c;
    color: #fff;
    text-align: center;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    margin: auto 0;
    padding: 0.5rem;
  }
</style>
