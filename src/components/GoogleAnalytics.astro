---
export interface Props {
  trackingId: string
}

const { trackingId = '' } = Astro.props
---

{
  trackingId && (
    <>
      <script
        is:inline
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${trackingId}`}
      />
      <script is:inline define:vars={{ trackingId }}>
        window.dataLayer = window.dataLayer || []; function gtag()
        {dataLayer.push(arguments)}
        gtag('js', new Date()); gtag('config', trackingId);
      </script>
    </>
  )
}
