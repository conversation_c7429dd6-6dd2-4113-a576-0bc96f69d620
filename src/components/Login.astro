---
import Authentication from './svelte/modules/Authentication.svelte'
---

<div class="container">
  <div class="area">
    <div class="area--header">
      <h1>Resource center</h1>
    </div>

    <div class="area--content">
      <p>Authentication is required to access this resource center.</p>
      <Authentication client:load />
    </div>

    <!-- <div class="area--footer">
      <p class="tagline">
         We invest in visionary founders building category-defining tech companies that are levered to Africa.  
      </p>
      <a href="/">Request invite</a>
    </div> -->
  </div>
</div>

<style>
  .container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .area--header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 1rem;
    height: 10vh;
  }

  .area--header h1 {
    margin: 2rem auto;
    text-transform: capitalize;
  }

  .area--content p {
    padding: 0 2rem;
    text-align: center;
  }

  img {
    display: block;
    object-fit: cover;
    height: 100%;
    object-position: 50% 50%;
    animation: moveElement 9s infinite;
    border-radius: 4px;
  }

  .area--content {
    width: 100%;
  }

  .area--footer {
    margin-top: auto;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .area--footer p {
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
    font-weight: 600;
  }

  .area--footer a {
    margin: 1rem auto;
    padding: 1rem 2rem;
    border-radius: 4px;
    color: var(--bg);
    background-color: var(--accents-1);
    text-decoration: none;
    font-weight: 600;
    text-transform: capitalize;
  }

  @keyframes moveElement {
    50% {
      /* transform: translateY(-5%); */
      border-radius: 50%;
    }
  }
</style>
