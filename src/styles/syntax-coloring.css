.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: slategray;
}

.token.namespace {
  opacity: 0.7;
}

.token.string,
.token.attr-value {
  color: #690;
}

.token.punctuation {
  color: #999;
}

.token.operator {
  color: #9a6e3a;
}

.token.entity,
.token.url,
.token.symbol,
.token.number,
.token.boolean,
.token.variable,
.token.constant,
.token.property,
.token.regex {
  color: #905;
}

.token.prefix.inserted {
  color: #690;
}

.token.prefix.deleted {
  color: #dd4a68;
}

.token.atrule,
.token.keyword,
.token.attr-name,
.language-autohotkey .token.selector {
  color: #07a;
}

.token.function,
.language-autohotkey .token.tag {
  color: #dd4a68;
}

.token.tag,
.token.selector,
.language-autohotkey .token.keyword {
  color: #00009f;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}
