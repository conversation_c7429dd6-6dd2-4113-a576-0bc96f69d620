.gray,
.gray:hover {
  color: rgba(120, 119, 116, 1);
}
.brown,
.brown:hover {
  color: rgba(159, 107, 83, 1);
}
.orange,
.orange:hover {
  color: rgba(217, 115, 13, 1);
}
.yellow,
.yellow:hover {
  color: rgba(203, 145, 47, 1);
}
.green,
.green:hover {
  color: rgba(68, 131, 97, 1);
}
.blue,
.blue:hover {
  color: rgba(51, 126, 169, 1);
}
.purple,
.purple:hover {
  color: rgba(144, 101, 176, 1);
}
.pink,
.pink:hover {
  color: rgba(193, 76, 138, 1);
}
.red,
.red:hover {
  color: rgba(212, 76, 71, 1);
}
.gray-background {
  background: rgba(241, 241, 239, 1) !important;
}
.brown-background {
  background: rgba(244, 238, 238, 1) !important;
}
.orange-background {
  background: rgba(251, 236, 221, 1) !important;
}
.yellow-background {
  background: rgba(251, 243, 219, 1) !important;
}
.green-background {
  background: rgba(237, 243, 236, 1) !important;
}
.blue-background {
  background: rgba(231, 243, 248, 1) !important;
}
.purple-background {
  background: rgba(244, 240, 247, 0.8) !important;
}
.pink-background {
  background: rgba(249, 238, 243, 0.8) !important;
}
.red-background {
  background: rgba(253, 235, 236, 1) !important;
}

.tag.light-gray {
  color: rgb(28, 56, 41);
  background: rgba(227, 226, 224, 0.5) !important;
}
.tag.gray {
  color: rgb(28, 56, 41);
  background: rgb(227, 226, 224) !important;
}
.tag.brown {
  color: rgb(28, 56, 41);
  background: rgb(238, 224, 218) !important;
}
.tag.orange {
  color: rgb(28, 56, 41);
  background: rgb(250, 222, 201) !important;
}
.tag.yellow {
  color: rgb(28, 56, 41);
  background: rgb(253, 236, 200) !important;
}
.tag.green {
  color: rgb(28, 56, 41);
  background: rgb(219, 237, 219) !important;
}
.tag.blue {
  color: rgb(28, 56, 41);
  background: rgb(211, 229, 239) !important;
}
.tag.purple {
  color: rgb(28, 56, 41);
  background: rgb(232, 222, 238) !important;
}
.tag.pink {
  color: rgb(28, 56, 41);
  background: rgb(245, 224, 233) !important;
}
.tag.red {
  color: rgb(28, 56, 41);
  background: rgb(255, 226, 221) !important;
}
