name: Fly Deploy
'on':
  push:
    branches:
      - main
  repository_dispatch:

jobs:
  deploy:
    name: Deploy app
    runs-on: ubuntu-latest
    timeout-minutes: 15
    concurrency: deploy-group
    steps:
      - uses: actions/checkout@v4
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: >
          flyctl deploy --build-arg SALEM_NOTION_API_SECRET='${{secrets.SALEM_NOTION_API_SECRET}}' 
          --build-arg FOUNDERS_DATABASE_ID='${{secrets.FOUNDERS_DATABASE_ID}}' 
          --build-arg LP_DATABASE_ID='${{secrets.LP_DATABASE_ID}}'
          --build-arg MONGODB_URI='${{ secrets.MONGODB_URI }}'
          --build-arg ZEPTO_API_KEY='${{ secrets.ZEPTO_API_KEY }}'
          --build-arg CRE_EMAIL_SENDER='${{ secrets.CRE_EMAIL_SENDER }}'
          --build-arg SECRET_KEY='${{ secrets.SECRET_KEY }}'
          --build-arg PUBLIC_SECRET_KEY='${{ secrets.PUBLIC_SECRET_KEY }}'
          --build-arg PUBLIC_HCAPTCHA_SITE_KEY='${{ secrets.PUBLIC_HCAPTCHA_SITE_KEY }}'
          --build-arg HCAPTCHA_SECRET_KEY='${{ secrets.HCAPTCHA_SECRET_KEY }}'
          --build-arg MAIL_CHIMP_KEY='${{ secrets.MAIL_CHIMP_KEY }}' --remote-only
        env:
          FLY_API_TOKEN: '${{ secrets.FLY_API_TOKEN }}'
          SALEM_NOTION_API_SECRET: '${{ secrets.SALEM_NOTION_API_SECRET }}'
          FOUNDERS_DATABASE_ID: '${{ secrets.FOUNDERS_DATABASE_ID }}'
          LP_DATABASE_ID: '${{ secrets.LP_DATABASE_ID }}'
          MONGODB_URI: '${{ secrets.MONGODB_URI }}'
          ZEPTO_API_KEY: '${{ secrets.ZEPTO_API_KEY }}'
          CRE_EMAIL_SENDER: '${{ secrets.CRE_EMAIL_SENDER }}'
          SECRET_KEY: '${{ secrets.SECRET_KEY }}'
          PUBLIC_SECRET_KEY: '${{ secrets.PUBLIC_SECRET_KEY }}'
          PUBLIC_HCAPTCHA_SITE_KEY: '${{ secrets.PUBLIC_HCAPTCHA_SITE_KEY }}'
          HCAPTCHA_SECRET_KEY: '${{ secrets.HCAPTCHA_SECRET_KEY }}'
          MAIL_CHIMP_KEY: '${{ secrets.MAIL_CHIMP_KEY }}'
