{"name": "cre-notion", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "build:cached": "npm run cache:fetch && astro build", "preview": "astro preview", "astro": "astro", "lint": "eslint --ext .js,.ts,.astro src", "format": "npx prettier --write .", "cache:fetch": "node scripts/blog-contents-cache.cjs", "_fetch-notion-blocks": "node scripts/retrieve-block-children.cjs"}, "dependencies": {"@astrojs/node": "^6.1.0", "@astrojs/rss": "^4.0.5", "@astrojs/svelte": "^4.0.4", "@astrojs/tailwind": "^5.1.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "@notionhq/client": "^2.2.14", "@supercharge/promise-pool": "^2.4.0", "@zerodevx/svelte-toast": "^0.9.5", "astro": "^3.6.4", "astro-icon": "^0.8.2", "async-retry": "^1.3.3", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "exif-be-gone": "^1.4.1", "jose": "^5.2.2", "katex": "^0.16.9", "mermaid": "^10.8.0", "metascraper": "^5.44.0", "metascraper-description": "^5.44.0", "metascraper-image": "^5.44.0", "metascraper-title": "^5.44.0", "mongoose": "^8.1.3", "nanostores": "^0.9.5", "prismjs": "^1.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.32.6", "svelte": "^4.2.11", "svelte-headless-table": "^0.18.2", "tailwindcss": "^3.4.1", "twemoji": "^14.0.2", "unirest": "^0.6.0", "zeptomail": "^5.0.0"}, "devDependencies": {"@flydotio/dockerfile": "^0.5.2", "@iconify/svelte": "^3.1.6", "@types/async-retry": "^1.4.8", "@types/js-base64": "^3.3.1", "@types/metascraper": "^5.14.3", "@types/metascraper-description": "^5.14.5", "@types/metascraper-image": "^5.14.5", "@types/metascraper-title": "^5.14.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/typescript-estree": "^6.8.0", "cli-progress": "^3.12.0", "eslint": "^8.56.0", "eslint-plugin-astro": "^0.27.2", "svelte-hcaptcha": "^0.1.1"}}