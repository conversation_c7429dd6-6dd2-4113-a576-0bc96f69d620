# syntax = docker/dockerfile:1

# Adjust NODE_VERSION as desired
ARG NODE_VERSION=20.5.1
FROM node:${NODE_VERSION}-slim as base

LABEL fly_launch_runtime="Node.js"

# Node.js app lives here
WORKDIR /app

# Set production environment
ENV NODE_ENV="production"


# Throw-away build stage to reduce size of final image
FROM base as build

# Install packages needed to build node modules
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y build-essential node-gyp pkg-config python-is-python3

# Install node modules
COPY --link package-lock.json package.json ./
RUN npm ci --include=dev

# Copy application code
COPY --link . .

ENV NODE_OPTIONS="--max_old_space_size=4096"

# Build application
# Use build secrets
ARG SALEM_NOTION_API_SECRET
ARG FOUNDERS_DATABASE_ID
ARG LP_DATABASE_ID
ARG MONGODB_URI
ARG ZEPTO_API_KEY
ARG CRE_EMAIL_SENDER
ARG SECRET_KEY
ARG MAIL_CHIMP_KEY
ARG PUBLIC_SECRET_KEY
ARG PUBLIC_HCAPTCHA_SITE_KEY
ARG HCAPTCHA_SECRET_KEY
ARG JOIN_SECRETS_PRIVATE_KEY
RUN echo "SALEM_NOTION_API_SECRET=$SALEM_NOTION_API_SECRET" && \
    echo "FOUNDERS_DATABASE_ID=$FOUNDERS_DATABASE_ID" && \
    echo "LP_DATABASE_ID=$LP_DATABASE_ID" && \
    echo "MONGODB_URI=$MONGODB_URI" && \
    echo "ZEPTO_API_KEY=$ZEPTO_API_KEY" && \
    echo "CRE_EMAIL_SENDER=$CRE_EMAIL_SENDER" && \
    echo "SECRET_KEY=$SECRET_KEY" && \
    echo "PUBLIC_SECRET_KEY=$PUBLIC_SECRET_KEY" && \
    echo "PUBLIC_HCAPTCHA_SITE_KEY=$PUBLIC_HCAPTCHA_SITE_KEY" && \
    echo "HCAPTCHA_SECRET_KEY=$HCAPTCHA_SECRET_KEY" && \
    echo "JOIN_SECRETS_PRIVATE_KEY=$JOIN_SECRETS_PRIVATE_KEY" && \
    echo "MAIL_CHIMP_KEY=$MAIL_CHIMP_KEY" && npm run build


# Remove development dependencies
RUN npm prune --omit=dev


# Final stage for app image
FROM base

# Copy built application
COPY --from=build /app /app

# Start the server by default, this can be overwritten at runtime
EXPOSE 4321

# add host 

ENV HOST=0.0.0.0
ENV PORT=4321

CMD ["node", "./dist/server/entry.mjs"]
