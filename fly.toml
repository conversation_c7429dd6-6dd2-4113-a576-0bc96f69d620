# fly.toml app configuration file generated for cre-notion-vc on 2024-01-17T15:34:17+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "cre-notion-vc"
primary_region = "sjc"

[build]

[http_service]
  internal_port = 4321
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 256
